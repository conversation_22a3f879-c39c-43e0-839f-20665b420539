#!/usr/bin/env python3
"""
BulkAI Desktop Application Build Script

This script automates the process of building a standalone executable (.exe)
from the BulkAI desktop application using PyInstaller.

Features:
- Checks and updates bulky.spec for standalone distribution
- Runs PyInstaller with proper configuration
- <PERSON><PERSON> build errors with clear feedback
- Verifies output executable
- Optionally cleans up temporary build files
"""

import os
import sys
import shutil
import subprocess
import time
import io
from pathlib import Path

# Simple print function that avoids emoji encoding issues
def safe_print(message):
    """Print message with emoji characters replaced for Windows compatibility."""
    # Replace common emoji with text equivalents
    replacements = {
        '🔍': '[CHECK]',
        '✅': '[OK]',
        '❌': '[ERROR]',
        '⚠️': '[WARNING]',
        '📋': '[INFO]',
        '🔧': '[CONFIG]',
        '🧹': '[CLEAN]',
        '🗑️': '[DELETE]',
        '🔨': '[BUILD]',
        '🎉': '[SUCCESS]',
        '📦': '[PACKAGE]',
        '💡': '[TIP]',
        '🚀': '[START]',
        '📊': '[STATS]',
        '💥': '[CRASH]',
    }

    for emoji, replacement in replacements.items():
        message = message.replace(emoji, replacement)

    print(message)


class BuildError(Exception):
    """Custom exception for build-related errors."""
    pass


class BulkAIBuilder:
    """Main builder class for BulkAI desktop application."""

    def __init__(self):
        self.project_root = Path.cwd()
        self.spec_file = self.project_root / "bulky.spec"
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.executable_name = "Azanx Bulk AI Images.exe"

    def check_prerequisites(self):
        """Check if all prerequisites are met."""
        safe_print("🔍 Checking prerequisites...")

        # Check if PyInstaller is installed
        try:
            result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"],
                                  capture_output=True, text=True, check=True)
            safe_print(f"✅ PyInstaller version: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise BuildError("❌ PyInstaller is not installed. Install it with: pip install pyinstaller")

        # Check if spec file exists
        if not self.spec_file.exists():
            raise BuildError(f"❌ Spec file not found: {self.spec_file}")
        safe_print(f"✅ Spec file found: {self.spec_file}")

        # Check if main.py exists
        main_py = self.project_root / "main.py"
        if not main_py.exists():
            raise BuildError(f"❌ Main script not found: {main_py}")
        safe_print(f"✅ Main script found: {main_py}")

        # Check if required resource files exist
        required_files = [
            "config.json",
            "test_prompts.txt",
            "ui/resources/app-logo.svg",
            "ui/resources/splash-screen.svg"
        ]

        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                safe_print(f"⚠️  Warning: Required file not found: {file_path}")
            else:
                safe_print(f"✅ Resource file found: {file_path}")

    def backup_spec_file(self):
        """Create a backup of the original spec file."""
        backup_path = self.spec_file.with_suffix('.spec.backup')
        if not backup_path.exists():
            shutil.copy2(self.spec_file, backup_path)
            safe_print(f"📋 Backup created: {backup_path}")
        else:
            safe_print(f"📋 Backup already exists: {backup_path}")

    def create_icon_from_svg(self):
        """Convert SVG to ICO format for PyInstaller."""
        try:
            from PIL import Image
            import cairosvg

            svg_path = self.project_root / "ui/resources/app-logo.svg"
            ico_path = self.project_root / "app-icon.ico"

            if ico_path.exists():
                safe_print(f"✅ Icon file already exists: {ico_path}")
                return str(ico_path)

            if not svg_path.exists():
                safe_print("⚠️  SVG icon not found, will build without icon")
                return None

            # Convert SVG to PNG first, then to ICO
            png_data = cairosvg.svg2png(url=str(svg_path), output_width=256, output_height=256)

            # Create ICO from PNG data
            img = Image.open(io.BytesIO(png_data))
            img.save(ico_path, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])

            safe_print(f"✅ Icon converted: {svg_path} -> {ico_path}")
            return str(ico_path)

        except ImportError:
            safe_print("⚠️  cairosvg not available, building without icon")
            safe_print("💡 To include an icon, install cairosvg: pip install cairosvg")
            return None
        except Exception as e:
            safe_print(f"⚠️  Failed to convert icon: {e}")
            return None

    def update_spec_file(self):
        """Update the spec file for standalone distribution."""
        print("🔧 Updating spec file for standalone distribution...")

        # Read the current spec file
        with open(self.spec_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check if it's already configured for onefile
        if 'exclude_binaries=False' in content and 'a.binaries,' in content:
            print("✅ Spec file already configured for standalone distribution")
            return

        # Try to create an icon
        icon_path = self.create_icon_from_svg()
        icon_line = f"icon='{icon_path}'," if icon_path else "icon=None,"

        # Create the updated spec file content for onefile distribution
        updated_content = '''# -*- mode: python ; coding: utf-8 -*-
import os
from PyInstaller.utils.hooks import collect_data_files

# Add data files
datas = [
    ('config.json', '.'),
    ('test_prompts.txt', '.'),
    ('ui/resources/app-logo.svg', 'ui/resources'),
    ('ui/resources/splash-screen.svg', 'ui/resources'),
    ('ui/resources/splash-screen-backup.svg', 'ui/resources'),
]

# Create generated_images directory if it doesn't exist
if not os.path.exists('generated_images'):
    os.makedirs('generated_images')

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.QtSvg',  # Added for SVG support
        'runware',
        'PIL',
        'PIL.Image',
        'requests'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Azanx Bulk AI Images',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    ''' + icon_line + '''
)
'''

        # Write the updated content
        with open(self.spec_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)

        print("✅ Spec file updated for standalone distribution")

    def clean_previous_builds(self):
        """Clean up previous build artifacts."""
        print("🧹 Cleaning previous build artifacts...")

        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            print(f"🗑️  Removed: {self.dist_dir}")

        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print(f"🗑️  Removed: {self.build_dir}")

    def run_pyinstaller(self):
        """Run PyInstaller to build the executable."""
        print("🔨 Building executable with PyInstaller...")
        print("This may take several minutes...")

        start_time = time.time()

        try:
            # Run PyInstaller with the spec file
            cmd = [sys.executable, "-m", "PyInstaller", "--clean", str(self.spec_file)]

            print(f"Running command: {' '.join(cmd)}")

            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )

            build_time = time.time() - start_time
            print(f"✅ Build completed successfully in {build_time:.1f} seconds")

            # Print build output for debugging if needed
            if result.stdout:
                print("\n📋 Build output:")
                print(result.stdout[-1000:])  # Last 1000 characters

        except subprocess.CalledProcessError as e:
            print(f"❌ Build failed with exit code {e.returncode}")
            print(f"Error output: {e.stderr}")
            if e.stdout:
                print(f"Standard output: {e.stdout}")
            raise BuildError(f"PyInstaller build failed: {e}")

    def verify_output(self):
        """Verify that the executable was created successfully."""
        print("🔍 Verifying build output...")

        executable_path = self.dist_dir / self.executable_name

        if not executable_path.exists():
            raise BuildError(f"❌ Executable not found: {executable_path}")

        file_size = executable_path.stat().st_size
        file_size_mb = file_size / (1024 * 1024)

        print(f"✅ Executable created: {executable_path}")
        print(f"📊 File size: {file_size_mb:.1f} MB")

        return executable_path

    def cleanup_build_files(self, keep_dist=True):
        """Clean up temporary build files."""
        print("🧹 Cleaning up temporary build files...")

        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print(f"🗑️  Removed build directory: {self.build_dir}")

        # Remove __pycache__ directories
        for pycache_dir in self.project_root.rglob("__pycache__"):
            if pycache_dir.is_dir():
                shutil.rmtree(pycache_dir)
                print(f"🗑️  Removed: {pycache_dir}")

        if not keep_dist:
            if self.dist_dir.exists():
                shutil.rmtree(self.dist_dir)
                print(f"🗑️  Removed dist directory: {self.dist_dir}")

    def build(self, clean_after=False):
        """Main build method that orchestrates the entire build process."""
        try:
            print("🚀 Starting BulkAI Desktop Application build process...")
            print("=" * 60)

            # Step 1: Check prerequisites
            self.check_prerequisites()

            # Step 2: Backup original spec file
            self.backup_spec_file()

            # Step 3: Update spec file for standalone distribution
            self.update_spec_file()

            # Step 4: Clean previous builds
            self.clean_previous_builds()

            # Step 5: Run PyInstaller
            self.run_pyinstaller()

            # Step 6: Verify output
            executable_path = self.verify_output()

            # Step 7: Optional cleanup
            if clean_after:
                self.cleanup_build_files(keep_dist=True)

            print("=" * 60)
            print("🎉 Build completed successfully!")
            print(f"📦 Executable location: {executable_path}")
            print(f"💡 You can now distribute this standalone executable to end users.")

            return executable_path

        except BuildError as e:
            print(f"\n❌ Build failed: {e}")
            return None
        except Exception as e:
            print(f"\n💥 Unexpected error: {e}")
            return None


def main():
    """Main entry point for the build script."""
    import argparse

    parser = argparse.ArgumentParser(description="Build BulkAI Desktop Application executable")
    parser.add_argument("--clean", action="store_true",
                       help="Clean up temporary build files after successful build")
    parser.add_argument("--no-backup", action="store_true",
                       help="Skip creating backup of spec file")

    args = parser.parse_args()

    builder = BulkAIBuilder()

    if not args.no_backup:
        builder.backup_spec_file()

    result = builder.build(clean_after=args.clean)

    if result:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
