import requests
import base64
from io import BytesIO
from PIL import Image
import json
from config_manager import ConfigManager
from logger import get_logger

class ReplicateAIClient:
    """Client for interacting with Replicate AI API for image generation."""

    BASE_URL = "https://api.replicate.com/v1/models"

    def __init__(self, api_key=None, config_manager=None):
        """Initialize the Replicate AI client.

        Args:
            api_key (str, optional): Replicate AI API key. If not provided,
                                     will look for key in config manager.
            config_manager (ConfigManager, optional): Config manager instance.
        """
        self.logger = get_logger()
        self.config_manager = config_manager or ConfigManager()

        # Get API key from parameter or config
        self.api_key = api_key
        if not self.api_key:
            self.api_key = self.config_manager.get_api_key("replicate_ai")
            self.logger.debug(f"Using API key from config: {'*' * 5}{self.api_key[-5:] if self.api_key else 'None'}")
        else:
            self.logger.debug(f"Using provided API key: {'*' * 5}{self.api_key[-5:] if self.api_key else 'None'}")

        if not self.api_key:
            self.logger.error("No API key found in config or provided as parameter")
            raise ValueError("API key must be provided or set in the config file for Replicate AI")

        # Load available models from config
        self._load_models()

    def _load_models(self):
        """Load available models from configuration."""
        config_models = self.config_manager.get_provider_models("replicate_ai")
        
        if not config_models or len(config_models) == 0:
            # If no models are defined in the config, use default models
            self.logger.warning("No Replicate AI models found in config, using default models")
            self.available_models = [
                {
                    "id": "black-forest-labs/flux-dev",
                    "name": "FLUX Dev",
                    "default_steps": 28,
                    "max_steps": 50,
                    "supports_aspect_ratio": True
                }
            ]
        else:
            # Use models from the configuration
            self.available_models = config_models

        self.logger.debug(f"Loaded {len(self.available_models)} Replicate AI models")

    def get_available_models(self):
        """Get list of available models.

        Returns:
            list: List of available models with id and name.
        """
        return self.available_models

    def _convert_dimensions_to_aspect_ratio(self, width, height):
        """Convert width and height to aspect ratio string.
        
        Args:
            width (int): Image width
            height (int): Image height
            
        Returns:
            str: Aspect ratio string (e.g., "1:1", "16:9")
        """
        # Calculate GCD to simplify the ratio
        def gcd(a, b):
            while b:
                a, b = b, a % b
            return a
        
        ratio_gcd = gcd(width, height)
        ratio_w = width // ratio_gcd
        ratio_h = height // ratio_gcd
        
        # Map common ratios to standard aspect ratios
        ratio_map = {
            (1, 1): "1:1",
            (16, 9): "16:9",
            (21, 9): "21:9",
            (3, 2): "3:2",
            (2, 3): "2:3",
            (4, 5): "4:5",
            (5, 4): "5:4",
            (3, 4): "3:4",
            (4, 3): "4:3",
            (9, 16): "9:16",
            (9, 21): "9:21"
        }
        
        # Check if we have a direct match
        if (ratio_w, ratio_h) in ratio_map:
            return ratio_map[(ratio_w, ratio_h)]
        
        # Default to closest standard ratio or 1:1
        if ratio_w == ratio_h:
            return "1:1"
        elif ratio_w > ratio_h:
            return "16:9"  # Landscape default
        else:
            return "9:16"  # Portrait default

    def generate_image(self, prompt, width=1024, height=768, steps=28, seed=None, model_id=None):
        """Generate an image using Replicate AI API.

        Args:
            prompt (str): Text prompt for image generation.
            width (int): Width of the image to generate.
            height (int): Height of the image to generate.
            steps (int): Number of inference steps.
            seed (int, optional): Random seed for reproducible generation.
            model_id (str, optional): Model ID to use for generation.

        Returns:
            PIL.Image.Image: Generated image as PIL Image object.

        Raises:
            Exception: If the API request fails.
        """
        if not self.api_key:
            raise ValueError("API key is required for Replicate AI")

        self.logger.debug(f"Generating image with Replicate AI: {prompt[:50]}...")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Prefer": "wait"
        }

        # Use the provided model_id or default to the first available model
        if not model_id:
            model_id = self.get_available_models()[0]["id"]
        
        self.logger.debug(f"Using Replicate AI model: {model_id}")

        # Convert dimensions to aspect ratio for Replicate AI
        aspect_ratio = self._convert_dimensions_to_aspect_ratio(width, height)
        self.logger.debug(f"Converted {width}x{height} to aspect ratio: {aspect_ratio}")

        # Prepare the request payload based on the provided schema
        payload = {
            "input": {
                "prompt": prompt,
                "aspect_ratio": aspect_ratio,
                "num_inference_steps": steps,
                "guidance": 3.5,  # Default guidance value
                "num_outputs": 1,
                "output_format": "png",
                "output_quality": 80,
                "megapixels": "1",
                "go_fast": True
            }
        }

        # Only add seed if it's not None and not -1 (which means random)
        if seed is not None and seed != -1:
            payload["input"]["seed"] = seed

        self.logger.debug(f"API request payload: {payload}")

        # Construct the full API URL
        api_url = f"{self.BASE_URL}/{model_id}/predictions"

        # Use a separate method for the actual API call
        return self._execute_api_request(headers, payload, api_url, width, height)

    def _execute_api_request(self, headers, payload, api_url, width=1024, height=768):
        """Execute the API request with robust error handling.

        Args:
            headers (dict): Request headers
            payload (dict): Request payload
            api_url (str): Full API URL
            width (int): Width of the image to generate
            height (int): Height of the image to generate

        Returns:
            PIL.Image.Image: Generated image as PIL Image object

        Raises:
            Exception: If the API request fails
        """
        try:
            # Make the API request
            self.logger.debug(f"Sending request to {api_url}")
            response = requests.post(api_url, headers=headers, json=payload)

            if response.status_code != 200:
                self.logger.error(f"API request failed with status code {response.status_code}")
                self.logger.error(f"Response content: {response.text}")
                raise Exception(f"Replicate AI API request failed with status code {response.status_code}: {response.text}")

            # Parse the response
            response_data = response.json()
            self.logger.debug("API request successful")

            # Extract the image URL from the response
            if "output" in response_data and response_data["output"]:
                image_url = response_data["output"][0] if isinstance(response_data["output"], list) else response_data["output"]
                
                # Download the image from the URL
                self.logger.debug(f"Downloading image from: {image_url}")
                image_response = requests.get(image_url)
                
                if image_response.status_code == 200:
                    # Convert to PIL Image
                    image = Image.open(BytesIO(image_response.content))
                    self.logger.debug(f"Successfully generated image: {image.size}")
                    return image
                else:
                    raise Exception(f"Failed to download image from URL: {image_url}")
            else:
                self.logger.error(f"No output found in response: {response_data}")
                raise Exception("No image output found in Replicate AI response")

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Network error during API request: {str(e)}")
            raise Exception(f"Network error during Replicate AI API request: {str(e)}")
        except Exception as e:
            self.logger.error(f"Error generating image with Replicate AI: {str(e)}", exc_info=True)
            raise Exception(f"Failed to generate image with Replicate AI: {str(e)}")

    def update_api_key(self, api_key):
        """Update the API key.

        Args:
            api_key (str): New API key to use.
        """
        self.logger.debug(f"Updating API key: {'*' * 5}{api_key[-5:] if api_key else 'None'}")
        self.api_key = api_key
