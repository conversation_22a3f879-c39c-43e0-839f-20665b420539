import sys
import traceback
import signal
from PyQt6.QtWidgets import QApp<PERSON>, QMessageBox
from PyQt6.QtCore import QTimer
from PyQt6.QtGui import QIcon
from ui.main_window import MainWindow
from ui.splash_screen import SplashScreen
from config_manager import ConfigManager
from logger import get_logger

# Global exception hook
def exception_hook(exctype, value, tb):
    """Global exception handler to prevent app from crashing."""
    logger = get_logger()
    logger.critical("Unhandled exception", exc_info=(exctype, value, tb))

    # Don't show error dialog for KeyboardInterrupt
    if exctype is not KeyboardInterrupt:
        # Format the traceback
        error_msg = ''.join(traceback.format_exception(exctype, value, tb))

        # Show error dialog
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle("Application Error")
        msg_box.setText("An unexpected error occurred:")
        msg_box.setDetailedText(error_msg)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg_box.exec()

    # Call the original exception hook
    sys.__excepthook__(exctype, value, tb)

# Signal handler for SIGINT (Ctrl+C)
def sigint_handler(*args):
    """Handler for SIGINT signal (Ctrl+C)."""
    logger = get_logger()
    logger.warning("SIGINT received, ignoring to prevent application crash")
    # We don't call sys.exit() here to prevent the app from closing

def main():
    """Main entry point for the application."""
    # Initialize logger
    logger = get_logger()
    logger.info("Starting Azanx Bulk AI Images application")

    # Set up global exception handler
    sys.excepthook = exception_hook
    logger.debug("Global exception handler installed")

    # Install SIGINT handler to prevent Ctrl+C from closing the app
    signal.signal(signal.SIGINT, sigint_handler)
    logger.debug("SIGINT handler installed")

    # Create the application
    app = QApplication(sys.argv)
    logger.debug("QApplication initialized")

    # Use a timer to allow Python to process signals (required for Windows)
    # This ensures our SIGINT handler works properly
    timer = QTimer()
    timer.start(500)  # 500ms interval
    timer.timeout.connect(lambda: None)  # Dummy function

    # Set application name and organization
    app.setApplicationName("Azanx Bulk AI Images")
    app.setOrganizationName("Azanx")

    # Set application icon
    app_icon = QIcon("ui/resources/app-logo.svg")
    app.setWindowIcon(app_icon)
    logger.debug("Application name, organization, and icon set")

    # Get the current theme
    config_manager = ConfigManager()
    theme = config_manager.get_setting("theme", "dark")

    # Create and show splash screen
    splash = SplashScreen(theme)
    splash.show()
    app.processEvents()  # Force the splash screen to be displayed immediately
    
    logger.debug("Splash screen created and shown")

    # Simulate loading with meaningful progress updates
    def create_main_window():
        logger.debug("Creating main window")
        window = MainWindow()
        return window

    # Use the static method to simulate loading with proper timing
    splash.set_message("Initializing application...")
    app.processEvents()

    # Simulate loading with actual work
    SplashScreen.simulate_loading(
        splash, 
        app, 
        min_duration=2.0,  # Minimum time to show splash
        max_duration=3.0   # Maximum time to prevent too long display
    )

    # Create main window after splash screen is visible
    window = create_main_window()
    
    # Ensure splash screen is visible for at least a moment before closing
    app.processEvents()
    
    # Finish splash screen with fade effect and show main window
    splash.finish(window)
    window.show()
    logger.info("Application UI displayed")

    # Start the event loop
    logger.debug("Starting event loop")
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
