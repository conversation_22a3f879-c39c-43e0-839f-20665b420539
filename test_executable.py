#!/usr/bin/env python3
"""
Test script to verify the built executable works correctly.
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def test_executable():
    """Test the built executable."""
    project_root = Path.cwd()
    executable_path = project_root / "dist" / "Azanx Bulk AI Images.exe"
    
    print("🧪 Testing BulkAI Desktop Application executable...")
    print("=" * 50)
    
    # Check if executable exists
    if not executable_path.exists():
        print(f"❌ Executable not found: {executable_path}")
        return False
    
    print(f"✅ Executable found: {executable_path}")
    
    # Get file size
    file_size = executable_path.stat().st_size
    file_size_mb = file_size / (1024 * 1024)
    print(f"📊 File size: {file_size_mb:.1f} MB")
    
    # Test if executable can start (just check if it launches without immediate crash)
    print("🚀 Testing executable launch...")
    try:
        # Start the process but don't wait for it to complete
        process = subprocess.Popen(
            [str(executable_path)],
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a few seconds to see if it crashes immediately
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Executable launched successfully and is running")
            
            # Terminate the process
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            
            print("✅ Executable terminated cleanly")
            return True
        else:
            # Process has already terminated
            stdout, stderr = process.communicate()
            print(f"❌ Executable terminated with exit code: {process.returncode}")
            if stderr:
                print(f"Error output: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to launch executable: {e}")
        return False


def main():
    """Main test function."""
    success = test_executable()
    
    print("=" * 50)
    if success:
        print("🎉 Executable test passed!")
        print("💡 The standalone executable is ready for distribution.")
    else:
        print("❌ Executable test failed!")
        print("💡 Check the build process and try again.")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
