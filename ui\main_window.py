import os
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QSlider, QSpinBox, QComboBox,
    QFileDialog, QMessageBox, QSplitter, QFrame, QScrollArea,
    QSizePolicy, QDialog, QTabWidget, QMenuBar, QStatusBar,
    QProgressBar, QApplication
)
from PyQt6.QtCore import Qt, QTimer, QEventLoop
from PyQt6.QtGui import QIcon, QAction, QKeySequence, QPixmap

from ui.styles import Styles
from ui.api_key_manager import ApiKeyManagerDialog
from config_manager import ConfigManager
from app_controller import AppController
from usage_tracker import UsageTracker
from logger import get_logger

class LicenseActivationDialog(QDialog):
    """Dialog for license activation."""

    def __init__(self, parent=None, license_manager=None):
        """Initialize the license activation dialog."""
        super().__init__(parent)
        self.license_manager = license_manager

        self.setWindowTitle("License Activation")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # Create layout
        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # Title
        title_label = QLabel("Activate Your BulkAI License")
        title_label.setObjectName("promptLabel")
        layout.addWidget(title_label)

        # Current status
        self.status_label = QLabel()
        self.status_label.setObjectName("infoLabel")
        layout.addWidget(self.status_label)

        # License key input
        key_layout = QVBoxLayout()
        key_label = QLabel("License Key:")
        self.key_input = QLineEdit()
        self.key_input.setPlaceholderText("BULKAI-XXXXXXXX-XXXXXXXX-XXXXXXXX")

        key_layout.addWidget(key_label)
        key_layout.addWidget(self.key_input)
        layout.addLayout(key_layout)

        # Plan information
        info_label = QLabel("License Plans:")
        info_label.setObjectName("infoLabel")
        layout.addWidget(info_label)

        plans_text = """
<b>Free Plan:</b> 10 images/day, Flux Schnell model only, Together AI only<br>
<b>Monthly Pro ($29.99):</b> Unlimited images, all models and providers<br>
<b>Yearly Pro ($299.99):</b> Unlimited images, all models and providers<br>
<b>Lifetime Pro ($999.99):</b> One-time purchase, unlimited access
        """
        plans_label = QLabel(plans_text)
        plans_label.setWordWrap(True)
        layout.addWidget(plans_label)

        # Buttons
        button_layout = QHBoxLayout()

        # Delete license button (only show if license is active)
        self.delete_button = QPushButton("Remove License")
        self.delete_button.setObjectName("deleteButton")
        self.delete_button.clicked.connect(self._delete_license)
        self.delete_button.setStyleSheet("""
            QPushButton#deleteButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton#deleteButton:hover {
                background-color: #c82333;
            }
            QPushButton#deleteButton:pressed {
                background-color: #bd2130;
            }
        """)

        self.activate_button = QPushButton("Activate License")
        self.activate_button.clicked.connect(self._activate_license)
        self.close_button = QPushButton("Close")
        self.close_button.setObjectName("secondaryButton")
        self.close_button.clicked.connect(self.reject)

        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)
        button_layout.addWidget(self.activate_button)
        layout.addLayout(button_layout)

        # Initial status update (now that all UI elements are created)
        self._update_status_display()

    def _update_status_display(self):
        """Update the license status display."""
        if not self.license_manager:
            self.status_label.setText("License manager not available")
            return

        verification = self.license_manager.verify_license()
        if verification.get('valid'):
            license_info = verification.get('license', {})
            plan_name = license_info.get('plan_name', 'Unknown')
            daily_limit = license_info.get('daily_image_limit', 0)
            limit_text = "Unlimited" if daily_limit == -1 else str(daily_limit)

            usage_info = verification.get('usage', {})
            today_usage = usage_info.get('today_usage', 0)

            self.status_label.setText(
                f"<b>Status:</b> Active License<br>"
                f"<b>Plan:</b> {plan_name}<br>"
                f"<b>Daily Limit:</b> {limit_text}<br>"
                f"<b>Today's Usage:</b> {today_usage}"
            )
        else:
            self.status_label.setText(
                "<b>Status:</b> Free Plan (No License)<br>"
                "<b>Daily Limit:</b> 10 images<br>"
                "<b>Features:</b> Flux Schnell model only, Together AI only"
            )

        # Update button visibility after status update
        self._update_button_visibility()

    def _update_button_visibility(self):
        """Update button visibility based on license status."""
        # Safety check: ensure delete_button exists before trying to access it
        if not hasattr(self, 'delete_button'):
            return

        if not self.license_manager:
            self.delete_button.hide()
            return

        verification = self.license_manager.verify_license()
        has_license = verification.get('valid', False)

        # Show delete button only if there's an active license
        if has_license:
            self.delete_button.show()
        else:
            self.delete_button.hide()

    def _activate_license(self):
        """Activate the entered license key."""
        license_key = self.key_input.text().strip()
        if not license_key:
            QMessageBox.warning(self, "Error", "Please enter a license key.")
            return

        # Show progress
        self.activate_button.setEnabled(False)
        self.activate_button.setText("Activating...")

        try:
            result = self.license_manager.activate_license(license_key)

            if result.get('success'):
                QMessageBox.information(
                    self,
                    "Success",
                    "License activated successfully! All Pro features are now unlocked."
                )
                self._update_status_display()
                self.key_input.clear()
                # Close the dialog to trigger UI refresh in main window
                self.accept()
            else:
                error_msg = result.get('error', 'Unknown error occurred')
                QMessageBox.critical(
                    self,
                    "Activation Failed",
                    f"Failed to activate license:\n{error_msg}"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred during activation:\n{str(e)}"
            )
        finally:
            self.activate_button.setEnabled(True)
            self.activate_button.setText("Activate License")

    def _delete_license(self):
        """Delete/deactivate the current license."""
        if not self.license_manager:
            QMessageBox.warning(self, "Error", "License manager not available.")
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "Remove License",
            "Are you sure you want to remove the license from this device?\n\n"
            "This will:\n"
            "• Deactivate the license on this device\n"
            "• Return you to the free plan\n"
            "• Allow you to use the license on another device\n\n"
            "You can reactivate the license later if needed.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # Show progress
        self.delete_button.setEnabled(False)
        self.delete_button.setText("Removing...")

        try:
            # Try to deactivate on server first
            result = self.license_manager.deactivate_license()

            if result.get('success'):
                QMessageBox.information(
                    self,
                    "License Removed",
                    "License has been successfully removed from this device.\n"
                    "You are now on the free plan."
                )
            else:
                # If server deactivation fails, offer local removal
                reply = QMessageBox.question(
                    self,
                    "Server Deactivation Failed",
                    f"Failed to deactivate license on server:\n{result.get('error', 'Unknown error')}\n\n"
                    "Would you like to remove the license locally only?\n"
                    "Note: This won't free up the license slot on the server.",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    if self.license_manager.delete_license_locally():
                        QMessageBox.information(
                            self,
                            "License Removed Locally",
                            "License has been removed from this device locally.\n"
                            "You are now on the free plan.\n\n"
                            "Note: The license slot may still be occupied on the server."
                        )
                    else:
                        QMessageBox.critical(
                            self,
                            "Error",
                            "Failed to remove license locally."
                        )

            # Update UI regardless of success/failure
            self._update_status_display()
            self.key_input.clear()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred while removing the license:\n{str(e)}"
            )
        finally:
            self.delete_button.setEnabled(True)
            self.delete_button.setText("Remove License")


class UpgradeDialog(QDialog):
    """Dialog for prompting users to upgrade to Pro plan."""

    def __init__(self, parent=None, message="", feature_name="this feature"):
        """Initialize the upgrade dialog."""
        super().__init__(parent)
        self.setWindowTitle("Upgrade Required")
        self.setMinimumWidth(500)
        self.setMinimumHeight(300)

        # Create layout
        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # Icon and title
        title_layout = QHBoxLayout()

        # You could add an icon here if you have one
        title_label = QLabel("🚀 Upgrade to Pro")
        title_label.setObjectName("promptLabel")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #007bff;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        layout.addLayout(title_layout)

        # Message
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("font-size: 16px; margin: 10px 0;")
        layout.addWidget(message_label)

        # Features comparison
        features_label = QLabel("Pro Plan Benefits:")
        features_label.setObjectName("infoLabel")
        features_label.setStyleSheet("font-weight: bold; margin-top: 20px;")
        layout.addWidget(features_label)

        benefits_text = """
✓ <b>Unlimited daily image generation</b><br>
✓ <b>Access to all AI models</b> (Runware AI, FLUX variants)<br>
✓ <b>Bulk generation feature</b> for processing multiple prompts<br>
✓ <b>All API providers</b> (Together AI + Runware AI)<br>
✓ <b>Priority support</b> and updates<br>
✓ <b>No daily limits or restrictions</b>
        """
        benefits_label = QLabel(benefits_text)
        benefits_label.setWordWrap(True)
        benefits_label.setStyleSheet("margin: 10px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px; color: #333333;")
        layout.addWidget(benefits_label)

        # Pricing info
        pricing_text = """
<b>Choose Your Plan:</b><br>
• Monthly Pro: $29.99/month<br>
• Yearly Pro: $299.99/year (Save 17%)<br>
• Lifetime Pro: $999.99 (One-time payment)
        """
        pricing_label = QLabel(pricing_text)
        pricing_label.setWordWrap(True)
        pricing_label.setStyleSheet("margin: 10px 0; font-size: 14px;")
        layout.addWidget(pricing_label)

        # Buttons
        button_layout = QHBoxLayout()

        self.later_button = QPushButton("Maybe Later")
        self.later_button.setObjectName("secondaryButton")
        self.later_button.clicked.connect(self.reject)

        self.upgrade_button = QPushButton("Upgrade Now")
        self.upgrade_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        self.upgrade_button.clicked.connect(self.accept)

        button_layout.addWidget(self.later_button)
        button_layout.addStretch()
        button_layout.addWidget(self.upgrade_button)
        layout.addLayout(button_layout)


class ApiKeyDialog(QDialog):
    """Dialog for entering API keys."""

    def __init__(self, parent=None, config_manager=None, license_manager=None):
        """Initialize the API key dialog."""
        super().__init__(parent)
        self.config_manager = config_manager
        self.license_manager = license_manager

        self.setWindowTitle("API Key Configuration")
        self.setMinimumWidth(500)

        # Create layout
        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # Title
        title_label = QLabel("Configure API Keys")
        title_label.setObjectName("promptLabel")
        layout.addWidget(title_label)

        # Together AI API key
        together_layout = QVBoxLayout()
        together_label = QLabel("Together AI API Key:")
        self.together_input = QLineEdit()
        self.together_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.together_input.setPlaceholderText("Enter your Together AI API key")

        # Load existing key if available
        if self.config_manager:
            api_key = self.config_manager.get_api_key("together_ai")
            if api_key:
                self.together_input.setText(api_key)

        together_layout.addWidget(together_label)
        together_layout.addWidget(self.together_input)
        layout.addLayout(together_layout)

        # Check if user can access Runware AI
        show_runware = True
        if self.license_manager and self.license_manager.is_trial_plan():
            show_runware = False

        # Runware AI API key (only show for Pro users)
        if show_runware:
            runware_layout = QVBoxLayout()
            runware_label = QLabel("Runware AI API Key:")
            self.runware_input = QLineEdit()
            self.runware_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.runware_input.setPlaceholderText("Enter your Runware AI API key")

            # Load existing key if available
            if self.config_manager:
                api_key = self.config_manager.get_api_key("runware_ai")
                if api_key:
                    self.runware_input.setText(api_key)

            runware_layout.addWidget(runware_label)
            runware_layout.addWidget(self.runware_input)
            layout.addLayout(runware_layout)
        else:
            # Create a hidden input for trial users
            self.runware_input = QLineEdit()
            self.runware_input.hide()

            # Show trial limitation message
            trial_message = QLabel("🔒 Runware AI is available with Pro plans only")
            trial_message.setStyleSheet("""
                QLabel {
                    color: #856404;
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    padding: 10px;
                    border-radius: 5px;
                    font-weight: bold;
                }
            """)
            layout.addWidget(trial_message)

        # API key info
        info_label = QLabel("You can get API keys from:")
        info_label.setObjectName("infoLabel")
        layout.addWidget(info_label)

        links_layout = QVBoxLayout()
        together_link = QLabel("<a href='https://api.together.xyz/settings/api-keys'>Together AI</a>")
        together_link.setOpenExternalLinks(True)
        links_layout.addWidget(together_link)

        if show_runware:
            runware_link = QLabel("<a href='https://my.runware.ai/'>Runware AI</a>")
            runware_link.setOpenExternalLinks(True)
            links_layout.addWidget(runware_link)

        layout.addLayout(links_layout)

        # Buttons
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("Save")
        self.save_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setObjectName("secondaryButton")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.save_button)
        layout.addLayout(button_layout)

    def get_together_api_key(self):
        """Get the entered Together AI API key."""
        return self.together_input.text().strip()

    def get_runware_api_key(self):
        """Get the entered Runware AI API key."""
        return self.runware_input.text().strip()

class MainWindow(QMainWindow):
    """Main window for the application."""

    def __init__(self):
        """Initialize the main window."""
        super().__init__()

        # Initialize logger
        self.logger = get_logger()
        self.logger.info("Initializing Azanx Bulk AI Images")

        # Initialize config manager
        self.config_manager = ConfigManager()
        self.logger.debug("Config manager initialized")
        # Initialize controller
        self.controller = AppController()
        self.logger.debug("App controller initialized")

        # Initialize usage tracker
        self.usage_tracker = UsageTracker(self.config_manager, self.controller.license_manager)
        self.logger.debug("Usage tracker initialized")

        # Flag to prevent double counting of usage
        self._generation_completed = False

        # Connect controller signals
        self._connect_controller_signals()

        # Connect usage tracker signals
        self._connect_usage_tracker_signals()

        # Set up the window
        self.setWindowTitle("Azanx Bulk AI Images")
        self.setMinimumSize(1200, 800)

        # Set window icon
        app_icon = QIcon("ui/resources/app-logo.svg")
        self.setWindowIcon(app_icon)

        # Set up the UI
        self._setup_ui()
        self._setup_menu()
        self._setup_status_bar()
        self._apply_theme()
        self.logger.debug("UI setup complete")
        # Check for API key
        self._check_api_key()

        # Populate both model dropdowns
        self._populate_model_dropdown()
        self._populate_bulk_model_dropdown()

        # Update license status display
        self._update_license_status()

        # Check bulk generation access on startup
        self._check_bulk_generation_access()

    def _setup_license_banner(self, main_layout):
        """Set up the license status banner."""
        self.license_banner = QFrame()
        self.license_banner.setObjectName("licenseBanner")
        self.license_banner.setMaximumHeight(50)

        banner_layout = QHBoxLayout(self.license_banner)
        banner_layout.setContentsMargins(15, 10, 15, 10)

        # License status text
        self.license_status_label = QLabel("Checking license status...")
        self.license_status_label.setObjectName("licenseStatusLabel")
        banner_layout.addWidget(self.license_status_label)

        banner_layout.addStretch()

        # Upgrade button (initially hidden)
        self.upgrade_button = QPushButton("Upgrade to Pro")
        self.upgrade_button.setObjectName("upgradeButton")
        self.upgrade_button.clicked.connect(self._show_upgrade_dialog)
        self.upgrade_button.hide()
        banner_layout.addWidget(self.upgrade_button)

        main_layout.addWidget(self.license_banner)

    def _update_license_status(self):
        """Update the license status display."""
        try:
            license_manager = self.controller.license_manager

            # Get status text
            status_text = license_manager.get_usage_status_text()
            plan_status = license_manager.get_plan_status_text()

            # Update banner text
            self.license_status_label.setText(f"{plan_status} | {status_text}")

            # Show/hide upgrade button based on plan
            if license_manager.is_trial_plan():
                self.upgrade_button.show()
                # Style banner for trial
                self.license_banner.setStyleSheet("""
                    QFrame#licenseBanner {
                        background-color: #fff3cd;
                        border: 1px solid #ffeaa7;
                        border-radius: 5px;
                    }
                    QLabel#licenseStatusLabel {
                        color: #856404;
                        font-weight: bold;
                    }
                    QPushButton#upgradeButton {
                        background-color: #007bff;
                        color: white;
                        border: none;
                        padding: 5px 15px;
                        border-radius: 3px;
                        font-weight: bold;
                    }
                    QPushButton#upgradeButton:hover {
                        background-color: #0056b3;
                    }
                """)
            else:
                self.upgrade_button.hide()
                # Style banner for pro
                self.license_banner.setStyleSheet("""
                    QFrame#licenseBanner {
                        background-color: #d4edda;
                        border: 1px solid #c3e6cb;
                        border-radius: 5px;
                    }
                    QLabel#licenseStatusLabel {
                        color: #155724;
                        font-weight: bold;
                    }
                """)

        except Exception as e:
            self.logger.error(f"Error updating license status: {e}")
            self.license_status_label.setText("License status unavailable")

    def _check_bulk_generation_access(self):
        """Check if user can access bulk generation and disable tab if needed."""
        try:
            license_manager = self.controller.license_manager

            if not license_manager.can_access_bulk_generation():
                # Disable the bulk generation tab
                self.tab_widget.setTabEnabled(1, False)  # Index 1 is bulk generation tab
                self.tab_widget.setTabToolTip(1, "Bulk Generation requires a Pro license")
            else:
                # Enable the bulk generation tab
                self.tab_widget.setTabEnabled(1, True)
                self.tab_widget.setTabToolTip(1, "")

        except Exception as e:
            self.logger.error(f"Error checking bulk generation access: {e}")

    def _show_upgrade_dialog(self):
        """Show upgrade dialog when upgrade button is clicked."""
        dialog = UpgradeDialog(
            self,
            "Upgrade to Pro to unlock all features including unlimited image generation, access to all AI models, and bulk generation capabilities.",
            "Pro features"
        )
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self._show_license_dialog()

    def _on_tab_changed(self, index):
        """Handle tab change events."""
        # Check if user is trying to access bulk generation tab (index 1)
        if index == 1:  # Bulk generation tab
            license_manager = self.controller.license_manager
            if not license_manager.can_access_bulk_generation():
                # Block access and show upgrade dialog
                self.tab_widget.setCurrentIndex(0)  # Switch back to image generator

                dialog = UpgradeDialog(
                    self,
                    license_manager.get_upgrade_message("Bulk Generation"),
                    "Bulk Generation"
                )
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    self._show_license_dialog()

    def _connect_controller_signals(self):
        """Connect controller signals to UI slots."""
        # Status updates
        self.controller.status_changed.connect(self._update_status)

        # Image generation
        self.controller.image_generated.connect(self._display_image)
        self.controller.generation_started.connect(self._on_generation_started)
        self.controller.generation_progress.connect(self._on_generation_progress)
        self.controller.generation_finished.connect(self._on_generation_finished)

        # Bulk generation
        self.controller.bulk_generation_started.connect(self._on_bulk_generation_started)
        self.controller.bulk_generation_progress.connect(self._on_bulk_generation_progress)
        self.controller.bulk_generation_finished.connect(self._on_bulk_generation_finished)
        self.controller.bulk_prompt_processing.connect(self._on_bulk_prompt_processing)

        # Error handling
        self.controller.error_occurred.connect(self._show_error)

    def _connect_usage_tracker_signals(self):
        """Connect usage tracker signals to UI slots."""
        # Usage updates
        self.usage_tracker.usage_updated.connect(self._on_usage_updated)
        self.usage_tracker.limit_exceeded.connect(self._on_limit_exceeded)
        self.usage_tracker.limit_warning.connect(self._on_limit_warning)
        self.usage_tracker.reset_occurred.connect(self._on_usage_reset)

    def _setup_ui(self):
        """Set up the user interface."""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Add license status banner
        self._setup_license_banner(main_layout)

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
        main_layout.addWidget(self.tab_widget)

        # Create image generator tab
        self.image_generator_tab = QWidget()
        self.tab_widget.addTab(self.image_generator_tab, "Image Generator")

        # Set up image generator tab
        self._setup_image_generator_tab()

        # Create bulk generation tab
        self.bulk_generation_tab = QWidget()
        self.tab_widget.addTab(self.bulk_generation_tab, "Bulk Generation")

        # Set up bulk generation tab
        self._setup_bulk_generation_tab()

    def _setup_image_generator_tab(self):
        """Set up the image generator tab."""
        # Create layout
        layout = QVBoxLayout(self.image_generator_tab)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)

        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)
        # Set splitter to maintain proportions and prevent automatic resizing
        splitter.setChildrenCollapsible(False)  # Prevent panels from collapsing
        layout.addWidget(splitter)

        # Left panel - Controls with scroll area
        left_scroll = QScrollArea()
        left_scroll.setWidgetResizable(True)
        left_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        left_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        left_scroll.setMinimumWidth(350)
        left_scroll.setMaximumWidth(450)

        # Left panel content
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(12)

        # Control frame
        control_frame = QFrame()
        control_frame.setObjectName("controlFrame")
        control_layout = QVBoxLayout(control_frame)
        control_layout.setSpacing(10)  # Add spacing between main sections
        control_layout.setContentsMargins(12, 12, 12, 12)  # Add padding

        # Prompt input
        prompt_label = QLabel("Enter your prompt:")
        prompt_label.setObjectName("promptLabel")
        self.prompt_input = QLineEdit()
        self.prompt_input.setPlaceholderText("Describe the image you want to generate...")
        self.prompt_input.setMinimumHeight(50)
        self.prompt_input.textChanged.connect(self._on_prompt_changed)

        control_layout.addWidget(prompt_label)
        control_layout.addWidget(self.prompt_input)
        control_layout.addSpacing(10)  # Add space after prompt input

        # Settings frame
        settings_frame = QFrame()
        settings_frame.setObjectName("settingsFrame")
        settings_layout = QVBoxLayout(settings_frame)
        settings_layout.setSpacing(8)  # Add vertical spacing between form elements
        settings_layout.setContentsMargins(10, 10, 10, 10)  # Add padding inside the frame

        # Settings title
        settings_title = QLabel("Settings")
        settings_title.setObjectName("promptLabel")
        settings_layout.addWidget(settings_title)

        # API Provider
        provider_layout = QHBoxLayout()
        provider_layout.setSpacing(10)
        provider_label = QLabel("API Provider:")
        provider_label.setMinimumWidth(120)
        provider_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.provider_combo = QComboBox()

        # Check license status to determine available providers
        license_manager = self.controller.license_manager
        if license_manager.is_trial_plan():
            # Trial users only get Together AI
            self.provider_combo.addItems(["Together AI"])
            self.provider_combo.setCurrentIndex(0)
            # Force Together AI for trial users
            self.config_manager.set_setting("default_provider", "together_ai")
        else:
            # Pro users get all providers
            self.provider_combo.addItems([
                "Together AI",
                "Runware AI",
                "Replicate AI"
            ])
            # Set the current provider from settings
            current_provider = self.config_manager.get_setting("default_provider", "together_ai")
            if current_provider == "together_ai":
                self.provider_combo.setCurrentIndex(0)
            elif current_provider == "runware_ai":
                self.provider_combo.setCurrentIndex(1)
            elif current_provider == "replicate_ai":
                self.provider_combo.setCurrentIndex(2)
            else:
                self.provider_combo.setCurrentIndex(0)  # Default to Together AI

        self.provider_combo.currentIndexChanged.connect(self._on_provider_changed)
        provider_layout.addWidget(provider_label)
        provider_layout.addWidget(self.provider_combo)
        settings_layout.addLayout(provider_layout)

        # Model selection
        model_layout = QHBoxLayout()
        model_layout.setSpacing(10)
        model_label = QLabel("Model:")
        model_label.setMinimumWidth(120)
        model_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.model_combo = QComboBox()
        # Models will be populated based on the selected provider
        self.model_combo.currentTextChanged.connect(self._on_model_changed)
        model_layout.addWidget(model_label)
        model_layout.addWidget(self.model_combo)
        settings_layout.addLayout(model_layout)
        settings_layout.addSpacing(8)  # Add space after model

        # Image Style selection
        style_layout = QHBoxLayout()
        style_layout.setSpacing(10)
        style_label = QLabel("Image Style:")
        style_label.setMinimumWidth(120)
        style_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.style_combo = QComboBox()
        self.style_combo.addItems([
            "None",
            "Photorealistic",
            "Digital Art",
            "Oil Painting",
            "Watercolor",
            "Anime/Manga",
            "Sketch/Drawing",
            "Cinematic",
            "Fantasy Art",
            "Abstract",
            "Vintage/Retro"
        ])
        # Set default style from config
        default_style = self.config_manager.get_setting("default_image_style", "None")
        style_index = self.style_combo.findText(default_style)
        if style_index >= 0:
            self.style_combo.setCurrentIndex(style_index)
        self.style_combo.currentTextChanged.connect(self._on_style_changed)
        style_layout.addWidget(style_label)
        style_layout.addWidget(self.style_combo)
        settings_layout.addLayout(style_layout)
        settings_layout.addSpacing(8)  # Add space after style

        # Resolution type
        res_type_layout = QHBoxLayout()
        res_type_layout.setSpacing(10)
        res_type_label = QLabel("Resolution Type:")
        res_type_label.setMinimumWidth(120)
        res_type_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.res_type_combo = QComboBox()
        self.res_type_combo.addItems([
            "Square (1:1)",
            "Landscape (16:9)",
            "Portrait (9:16)",
            "Custom"
        ])
        self.res_type_combo.currentIndexChanged.connect(self._on_resolution_type_changed)
        res_type_layout.addWidget(res_type_label)
        res_type_layout.addWidget(self.res_type_combo)
        settings_layout.addLayout(res_type_layout)
        settings_layout.addSpacing(8)  # Add space after resolution type

        # Image size
        size_layout = QHBoxLayout()
        size_layout.setSpacing(10)
        size_label = QLabel("Image Size:")
        size_label.setMinimumWidth(120)
        size_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.size_combo = QComboBox()
        # Will be populated based on resolution type
        size_layout.addWidget(size_label)
        size_layout.addWidget(self.size_combo)
        settings_layout.addLayout(size_layout)
        settings_layout.addSpacing(8)  # Add space after image size

        # Custom dimensions (initially hidden)
        self.custom_dim_layout = QHBoxLayout()
        self.width_label = QLabel("Width:")
        self.width_spin = QSpinBox()
        self.width_spin.setMinimum(64)
        self.width_spin.setMaximum(1408)
        self.width_spin.setSingleStep(64)
        self.width_spin.setValue(1024)
        self.width_spin.valueChanged.connect(self._adjust_width_to_multiple_of_64)

        self.height_label = QLabel("Height:")
        self.height_spin = QSpinBox()
        self.height_spin.setMinimum(64)
        self.height_spin.setMaximum(1408)
        self.height_spin.setSingleStep(64)
        self.height_spin.setValue(768)
        self.height_spin.valueChanged.connect(self._adjust_height_to_multiple_of_64)

        self.custom_dim_layout.addWidget(self.width_label)
        self.custom_dim_layout.addWidget(self.width_spin)
        self.custom_dim_layout.addWidget(self.height_label)
        self.custom_dim_layout.addWidget(self.height_spin)
        settings_layout.addLayout(self.custom_dim_layout)
        settings_layout.addSpacing(8)  # Add space after custom dimensions

        # Initialize resolution options
        self._populate_size_options()

        # Hide custom dimensions initially
        for widget in [self.width_label, self.width_spin, self.height_label, self.height_spin]:
            widget.setVisible(False)

        # Steps
        steps_layout = QHBoxLayout()
        steps_layout.setSpacing(10)
        steps_label = QLabel("Steps:")
        steps_label.setMinimumWidth(120)
        steps_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.steps_slider = QSlider(Qt.Orientation.Horizontal)
        self.steps_slider.setMinimum(1)
        self.steps_slider.setMaximum(50)  # Default to 50 max steps
        self.steps_slider.setValue(28)    # Default to 28 steps
        self.steps_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.steps_slider.setTickInterval(5)  # Increased tick interval for larger range
        self.steps_spin = QSpinBox()
        self.steps_spin.setMinimum(1)
        self.steps_spin.setMaximum(50)    # Default to 50 max steps
        self.steps_spin.setValue(28)      # Default to 28 steps
        steps_layout.addWidget(steps_label)
        steps_layout.addWidget(self.steps_slider)
        steps_layout.addWidget(self.steps_spin)
        settings_layout.addLayout(steps_layout)

        # Connect steps slider and spin box
        self.steps_slider.valueChanged.connect(self.steps_spin.setValue)
        self.steps_spin.valueChanged.connect(self.steps_slider.setValue)

        # Seed
        seed_layout = QHBoxLayout()
        seed_layout.setSpacing(10)
        seed_label = QLabel("Seed:")
        seed_label.setMinimumWidth(120)
        seed_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.seed_spin = QSpinBox()
        self.seed_spin.setMinimum(-1)
        self.seed_spin.setMaximum(9999999)
        self.seed_spin.setValue(-1)
        self.seed_spin.setSpecialValueText("Random")
        seed_layout.addWidget(seed_label)
        seed_layout.addWidget(self.seed_spin)
        settings_layout.addLayout(seed_layout)

        # Auto-generate feature has been removed

        # Buttons
        button_layout = QHBoxLayout()

        # Generate button
        self.generate_button = QPushButton("Generate Image")
        self.generate_button.setMinimumHeight(50)
        self.generate_button.setIcon(QIcon("ui/resources/generate.png"))
        self.generate_button.clicked.connect(self._generate_image)

        # Save button
        self.save_button = QPushButton("Save Image")
        self.save_button.setMinimumHeight(50)
        self.save_button.setEnabled(False)
        self.save_button.setIcon(QIcon("ui/resources/save.png"))
        self.save_button.clicked.connect(self._save_image)

        button_layout.addWidget(self.generate_button)
        button_layout.addWidget(self.save_button)

        # Add to control layout
        control_layout.addWidget(settings_frame)
        control_layout.addSpacing(15)  # Add space before buttons
        control_layout.addLayout(button_layout)

        # Progress section
        progress_frame = QFrame()
        progress_frame.setObjectName("settingsFrame")
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setSpacing(10)  # Add spacing between progress elements
        progress_layout.setContentsMargins(10, 10, 10, 10)  # Add padding

        # Progress title
        progress_title = QLabel("Generation Progress")
        progress_title.setObjectName("promptLabel")
        progress_layout.addWidget(progress_title)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)

        # Current status label
        self.generation_status_label = QLabel("No generation in progress")
        progress_layout.addWidget(self.generation_status_label)

        # Add progress frame to control layout
        control_layout.addSpacing(15)  # Add space before progress section
        control_layout.addWidget(progress_frame)

        # Add to left layout
        left_layout.addWidget(control_frame)
        left_layout.addStretch()

        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setObjectName("statusLabel")
        left_layout.addWidget(self.status_label)

        # Right panel - Image display
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # Image frame
        image_frame = QFrame()
        image_frame.setObjectName("imageFrame")
        image_layout = QVBoxLayout(image_frame)

        # Image title
        image_title = QLabel("Generated Image")
        image_title.setObjectName("promptLabel")
        image_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        image_layout.addWidget(image_title)

        # Image display
        self.image_label = QLabel("No image generated yet")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setMinimumSize(512, 512)
        self.image_label.setSizePolicy(
            QSizePolicy.Policy.Expanding,
            QSizePolicy.Policy.Expanding
        )

        # Scroll area for image
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.image_label)
        scroll_area.setWidgetResizable(True)
        image_layout.addWidget(scroll_area)

        right_layout.addWidget(image_frame)

        # Set the content widget to scroll area
        left_scroll.setWidget(left_panel)

        # Add panels to splitter
        splitter.addWidget(left_scroll)
        splitter.addWidget(right_panel)

        # Set equal stretch factors to maintain 50/50 split permanently
        splitter.setStretchFactor(0, 1)  # Left panel stretch factor
        splitter.setStretchFactor(1, 1)  # Right panel stretch factor
        splitter.setSizes([600, 600])  # Initial equal width distribution

        # Store splitter reference to maintain proportions
        self.main_splitter = splitter

    def _setup_bulk_generation_tab(self):
        """Set up the bulk generation tab."""
        # Create layout
        layout = QVBoxLayout(self.bulk_generation_tab)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)

        # Create scroll area for the bulk generation tab
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Create a widget to hold all controls
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(20, 20, 20, 20)
        scroll_layout.setSpacing(20)

        # Create frame for controls
        control_frame = QFrame()
        control_frame.setObjectName("controlFrame")
        control_layout = QVBoxLayout(control_frame)
        control_layout.setSpacing(10)  # Add spacing between main sections
        control_layout.setContentsMargins(12, 12, 12, 12)  # Add padding
        control_layout.setSpacing(15)

        # Title
        bulk_title = QLabel("Bulk Image Generation")
        bulk_title.setObjectName("promptLabel")
        control_layout.addWidget(bulk_title)

        # Description
        description = QLabel("Generate multiple images from prompts in a text file (one prompt per line)")
        control_layout.addWidget(description)

        # Rate limit warning
        rate_limit_warning = QLabel("Note: The free FLUX.1-schnell model is limited to 9 images per minute.")
        rate_limit_warning.setStyleSheet("color: #FF7700;")
        control_layout.addWidget(rate_limit_warning)

        # File selection
        file_layout = QHBoxLayout()
        file_label = QLabel("Prompts File:")
        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("Select a text file with prompts...")
        self.file_path_input.setReadOnly(True)

        browse_button = QPushButton("Browse")
        browse_button.clicked.connect(self._browse_prompts_file)

        file_layout.addWidget(file_label)
        file_layout.addWidget(self.file_path_input)
        file_layout.addWidget(browse_button)
        control_layout.addLayout(file_layout)

        # Settings frame
        settings_frame = QFrame()
        settings_frame.setObjectName("settingsFrame")
        settings_layout = QVBoxLayout(settings_frame)
        settings_layout.setSpacing(8)  # Add vertical spacing between form elements
        settings_layout.setContentsMargins(10, 10, 10, 10)  # Add padding inside the frame

        # Settings title
        settings_title = QLabel("Generation Settings")
        settings_title.setObjectName("promptLabel")
        settings_layout.addWidget(settings_title)

        # API Provider (interactive dropdown)
        provider_layout = QHBoxLayout()
        provider_layout.setSpacing(10)
        provider_label = QLabel("API Provider:")
        provider_label.setMinimumWidth(120)
        provider_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.bulk_provider_combo = QComboBox()

        # Check license status to determine available providers
        license_manager = self.controller.license_manager
        if license_manager.is_trial_plan():
            # Trial users only get Together AI
            self.bulk_provider_combo.addItems(["Together AI"])
            self.bulk_provider_combo.setCurrentIndex(0)
        else:
            # Pro users get all providers
            self.bulk_provider_combo.addItems([
                "Together AI",
                "Runware AI",
                "Replicate AI"
            ])
            # Set the current provider from settings
            current_provider = self.config_manager.get_setting("default_provider", "together_ai")
            if current_provider == "together_ai":
                self.bulk_provider_combo.setCurrentIndex(0)
            elif current_provider == "runware_ai":
                self.bulk_provider_combo.setCurrentIndex(1)
            elif current_provider == "replicate_ai":
                self.bulk_provider_combo.setCurrentIndex(2)
            else:
                self.bulk_provider_combo.setCurrentIndex(0)  # Default to Together AI

        self.bulk_provider_combo.currentIndexChanged.connect(self._on_bulk_provider_changed)
        provider_layout.addWidget(provider_label)
        provider_layout.addWidget(self.bulk_provider_combo)
        settings_layout.addLayout(provider_layout)

        # Model selection (interactive dropdown)
        model_layout = QHBoxLayout()
        model_layout.setSpacing(10)
        model_label = QLabel("Model:")
        model_label.setMinimumWidth(120)
        model_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.bulk_model_combo = QComboBox()
        # Models will be populated based on the selected provider
        self.bulk_model_combo.currentTextChanged.connect(self._on_bulk_model_changed)
        model_layout.addWidget(model_label)
        model_layout.addWidget(self.bulk_model_combo)
        settings_layout.addLayout(model_layout)

        # Image Style selection for bulk generation
        bulk_style_layout = QHBoxLayout()
        bulk_style_layout.setSpacing(10)
        bulk_style_label = QLabel("Image Style:")
        bulk_style_label.setMinimumWidth(120)
        bulk_style_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.bulk_style_combo = QComboBox()
        self.bulk_style_combo.addItems([
            "None",
            "Photorealistic",
            "Digital Art",
            "Oil Painting",
            "Watercolor",
            "Anime/Manga",
            "Sketch/Drawing",
            "Cinematic",
            "Fantasy Art",
            "Abstract",
            "Vintage/Retro"
        ])
        # Set default style from config
        default_style = self.config_manager.get_setting("default_image_style", "None")
        style_index = self.bulk_style_combo.findText(default_style)
        if style_index >= 0:
            self.bulk_style_combo.setCurrentIndex(style_index)
        self.bulk_style_combo.currentTextChanged.connect(self._on_bulk_style_changed)
        bulk_style_layout.addWidget(bulk_style_label)
        bulk_style_layout.addWidget(self.bulk_style_combo)
        settings_layout.addLayout(bulk_style_layout)

        # Resolution type
        bulk_res_type_layout = QHBoxLayout()
        bulk_res_type_layout.setSpacing(10)
        bulk_res_type_label = QLabel("Resolution Type:")
        bulk_res_type_label.setMinimumWidth(120)
        bulk_res_type_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.bulk_res_type_combo = QComboBox()
        self.bulk_res_type_combo.addItems([
            "Square (1:1)",
            "Landscape (16:9)",
            "Portrait (9:16)",
            "Custom"
        ])
        self.bulk_res_type_combo.currentIndexChanged.connect(self._on_bulk_resolution_type_changed)
        bulk_res_type_layout.addWidget(bulk_res_type_label)
        bulk_res_type_layout.addWidget(self.bulk_res_type_combo)
        settings_layout.addLayout(bulk_res_type_layout)

        # Image size
        size_layout = QHBoxLayout()
        size_label = QLabel("Image Size:")
        self.bulk_size_combo = QComboBox()
        # Will be populated based on resolution type
        size_layout.addWidget(size_label)
        size_layout.addWidget(self.bulk_size_combo)
        settings_layout.addLayout(size_layout)

        # Custom dimensions (initially hidden)
        self.bulk_custom_dim_layout = QHBoxLayout()
        self.bulk_width_label = QLabel("Width:")
        self.bulk_width_spin = QSpinBox()
        self.bulk_width_spin.setMinimum(64)
        self.bulk_width_spin.setMaximum(1408)
        self.bulk_width_spin.setSingleStep(64)
        self.bulk_width_spin.setValue(1024)
        self.bulk_width_spin.valueChanged.connect(self._adjust_bulk_width_to_multiple_of_64)

        self.bulk_height_label = QLabel("Height:")
        self.bulk_height_spin = QSpinBox()
        self.bulk_height_spin.setMinimum(64)
        self.bulk_height_spin.setMaximum(1408)
        self.bulk_height_spin.setSingleStep(64)
        self.bulk_height_spin.setValue(768)
        self.bulk_height_spin.valueChanged.connect(self._adjust_bulk_height_to_multiple_of_64)

        self.bulk_custom_dim_layout.addWidget(self.bulk_width_label)
        self.bulk_custom_dim_layout.addWidget(self.bulk_width_spin)
        self.bulk_custom_dim_layout.addWidget(self.bulk_height_label)
        self.bulk_custom_dim_layout.addWidget(self.bulk_height_spin)
        settings_layout.addLayout(self.bulk_custom_dim_layout)

        # Initialize resolution options
        self._populate_bulk_size_options()

        # Hide custom dimensions initially
        for widget in [self.bulk_width_label, self.bulk_width_spin, self.bulk_height_label, self.bulk_height_spin]:
            widget.setVisible(False)

        # Steps
        steps_layout = QHBoxLayout()
        steps_layout.setSpacing(10)
        steps_label = QLabel("Steps:")
        steps_label.setMinimumWidth(120)
        steps_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.bulk_steps_slider = QSlider(Qt.Orientation.Horizontal)
        self.bulk_steps_slider.setMinimum(1)
        self.bulk_steps_slider.setMaximum(50)  # Default to 50 max steps
        self.bulk_steps_slider.setValue(28)    # Default to 28 steps
        self.bulk_steps_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.bulk_steps_slider.setTickInterval(5)  # Increased tick interval for larger range
        self.bulk_steps_spin = QSpinBox()
        self.bulk_steps_spin.setMinimum(1)
        self.bulk_steps_spin.setMaximum(50)    # Default to 50 max steps
        self.bulk_steps_spin.setValue(28)      # Default to 28 steps
        steps_layout.addWidget(steps_label)
        steps_layout.addWidget(self.bulk_steps_slider)
        steps_layout.addWidget(self.bulk_steps_spin)
        settings_layout.addLayout(steps_layout)

        # Connect steps slider and spin box
        self.bulk_steps_slider.valueChanged.connect(self.bulk_steps_spin.setValue)
        self.bulk_steps_spin.valueChanged.connect(self.bulk_steps_slider.setValue)

        # Output folder
        output_layout = QHBoxLayout()
        output_label = QLabel("Output Folder:")
        self.output_folder_input = QLineEdit()
        self.output_folder_input.setPlaceholderText("Default: generated_images/bulk_<timestamp>")
        self.output_folder_input.setReadOnly(True)

        output_browse_button = QPushButton("Browse")
        output_browse_button.clicked.connect(self._browse_output_folder)

        output_layout.addWidget(output_label)
        output_layout.addWidget(self.output_folder_input)
        output_layout.addWidget(output_browse_button)
        settings_layout.addLayout(output_layout)

        # Add settings to control layout
        control_layout.addWidget(settings_frame)

        # Generate button
        self.bulk_generate_button = QPushButton("Start Bulk Generation")
        self.bulk_generate_button.setMinimumHeight(50)
        self.bulk_generate_button.setIcon(QIcon("ui/resources/generate.png"))
        self.bulk_generate_button.clicked.connect(self._start_bulk_generation)
        self.bulk_generate_button.setEnabled(False)  # Disabled until file is selected
        control_layout.addWidget(self.bulk_generate_button)

        # Progress section
        progress_frame = QFrame()
        progress_frame.setObjectName("settingsFrame")
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setSpacing(10)  # Add spacing between progress elements
        progress_layout.setContentsMargins(10, 10, 10, 10)  # Add padding

        # Progress title
        progress_title = QLabel("Generation Progress")
        progress_title.setObjectName("promptLabel")
        progress_layout.addWidget(progress_title)

        # Progress bar
        self.bulk_progress_bar = QProgressBar()
        self.bulk_progress_bar.setValue(0)
        progress_layout.addWidget(self.bulk_progress_bar)

        # Current prompt label
        self.current_prompt_label = QLabel("No generation in progress")
        progress_layout.addWidget(self.current_prompt_label)

        # Status label
        self.bulk_status_label = QLabel("Ready")
        self.bulk_status_label.setObjectName("statusLabel")
        progress_layout.addWidget(self.bulk_status_label)

        control_layout.addWidget(progress_frame)

        # Add control frame to scroll layout
        scroll_layout.addWidget(control_frame)

        # Set the scroll content and add scroll area to main layout
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

    def _setup_menu(self):
        """Set up the menu bar."""
        # Create menu bar
        menu_bar = QMenuBar()
        self.setMenuBar(menu_bar)

        # File menu
        file_menu = menu_bar.addMenu("File")

        # Save action
        save_action = QAction("Save Image", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self._save_image)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        # Exit action
        exit_action = QAction("Exit", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Settings menu
        settings_menu = menu_bar.addMenu("Settings")

        # License activation action
        license_action = QAction("License Activation", self)
        license_action.triggered.connect(self._show_license_dialog)
        settings_menu.addAction(license_action)

        settings_menu.addSeparator()

        # API key action
        api_key_action = QAction("Manage API Keys", self)
        api_key_action.triggered.connect(self._show_api_key_manager)
        settings_menu.addAction(api_key_action)

        settings_menu.addSeparator()

        # Theme submenu
        theme_menu = settings_menu.addMenu("Theme")

        # Dark theme action
        dark_theme_action = QAction("Dark", self)
        dark_theme_action.triggered.connect(lambda: self._set_theme("dark"))
        theme_menu.addAction(dark_theme_action)

        # Light theme action
        light_theme_action = QAction("Light", self)
        light_theme_action.triggered.connect(lambda: self._set_theme("light"))
        theme_menu.addAction(light_theme_action)

        # Help menu
        help_menu = menu_bar.addMenu("Help")

        # About action
        about_action = QAction("About", self)
        about_action.triggered.connect(self._show_about_dialog)
        help_menu.addAction(about_action)

    def _setup_status_bar(self):
        """Set up the status bar."""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # Add app name to the left side
        status_bar.addWidget(QLabel("Azanx Bulk AI Images"))

        # Add real-time image counter in the middle
        self.image_counter_label = QLabel("Images: 0/10")
        self.image_counter_label.setObjectName("imageCounterLabel")
        self.image_counter_label.setStyleSheet("""
            QLabel#imageCounterLabel {
                font-weight: bold;
                padding: 2px 8px;
                margin: 0 10px;
                border-radius: 3px;
                background-color: rgba(0, 123, 255, 0.1);
                color: #007bff;
            }
        """)
        status_bar.addPermanentWidget(self.image_counter_label)

        # Add "Made with love in Pakistan" to the right side
        love_label = QLabel("Made with love in Pakistan")
        love_label.setObjectName("loveLabel")
        status_bar.addPermanentWidget(love_label)

        # Initialize the counter display
        self._update_image_counter_display()

    def _apply_theme(self):
        """Apply the current theme."""
        theme = self.config_manager.get_setting("theme", "dark")
        self._set_theme(theme)

    def _set_theme(self, theme):
        """Set the application theme.

        Args:
            theme (str): Theme name ("dark" or "light").
        """
        if theme == "dark":
            self.setStyleSheet(Styles.get_dark_theme())
        else:
            self.setStyleSheet(Styles.get_light_theme())

        # Save theme setting
        self.config_manager.set_setting("theme", theme)

    def _set_steps_range(self, min_value, max_value, current_value):
        """Set the range and value for both steps sliders and spin boxes.

        Args:
            min_value (int): Minimum value for the steps controls
            max_value (int): Maximum value for the steps controls
            current_value (int): Current value to set for the steps controls
        """
        # Update main tab steps controls
        self.steps_slider.setMinimum(min_value)
        self.steps_slider.setMaximum(max_value)
        self.steps_slider.setValue(current_value)
        self.steps_spin.setMinimum(min_value)
        self.steps_spin.setMaximum(max_value)
        self.steps_spin.setValue(current_value)

        # Update bulk tab steps controls
        self.bulk_steps_slider.setMinimum(min_value)
        self.bulk_steps_slider.setMaximum(max_value)
        self.bulk_steps_slider.setValue(current_value)
        self.bulk_steps_spin.setMinimum(min_value)
        self.bulk_steps_spin.setMaximum(max_value)
        self.bulk_steps_spin.setValue(current_value)

        self.logger.debug(f"Updated steps range to {min_value}-{max_value}, current value: {current_value}")

    def _check_api_key(self):
        """Check if API keys are configured."""
        # Get the current provider from the dropdown (not config)
        provider_index = self.provider_combo.currentIndex()
        provider = "together_ai" if provider_index == 0 else "runware_ai"
        self.logger.debug(f"Populating model dropdown for provider: {provider} (index: {provider_index})")

        # Check the API key for the current provider
        if provider == "together_ai":
            api_key = self.config_manager.get_api_key("together_ai")
            self.logger.debug(f"Checking Together AI API key: {'Found' if api_key else 'Not found'}")

            if api_key:
                # Initialize the API client with the existing key
                self.controller.update_api_key(api_key, "together_ai")
                self.logger.info("Together AI API key loaded from config file")
            else:
                self.logger.warning("No Together AI API key found in config, showing dialog")
                # Show API key manager for first-time setup
                reply = QMessageBox.question(
                    self,
                    "API Key Required",
                    "No API key found for Together AI. Would you like to configure your API keys now?\n\n"
                    "You can also access the API Key Manager from Settings menu later.",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if reply == QMessageBox.StandardButton.Yes:
                    self._show_api_key_manager()
                    # Check again after manager closes
                    api_key = self.config_manager.get_api_key("together_ai")
                    if api_key:
                        self.controller.update_api_key(api_key, "together_ai")
                        self.logger.info("Together AI API key configured successfully")
                    else:
                        QMessageBox.information(
                            self,
                            "API Key Required",
                            "You can configure your API keys anytime from Settings > Manage API Keys."
                        )

        elif provider == "runware_ai":
            api_key = self.config_manager.get_api_key("runware_ai")
            self.logger.debug(f"Checking Runware AI API key: {'Found' if api_key else 'Not found'}")

            if api_key:
                # Initialize the API client with the existing key
                self.controller.update_api_key(api_key, "runware_ai")
                self.logger.info("Runware AI API key loaded from config file")
            else:
                self.logger.warning("No Runware AI API key found in config")
                # For Runware AI, just fall back to Together AI if available
                together_api_key = self.config_manager.get_api_key("together_ai")
                if together_api_key:
                    self.logger.info("Falling back to Together AI provider")
                    self.config_manager.set_setting("default_provider", "together_ai")
                    self.controller.update_api_key(together_api_key, "together_ai")
                else:
                    # No fallback available, inform user
                    QMessageBox.information(
                        self,
                        "API Key Required",
                        "No API key found for Runware AI. You can configure API keys from Settings > Manage API Keys."
                    )

        else:
            self.logger.warning(f"Unknown provider: {provider}, falling back to Together AI")
            # Fall back to Together AI
            self.config_manager.set_setting("default_provider", "together_ai")
            api_key = self.config_manager.get_api_key("together_ai")

            if api_key:
                self.controller.update_api_key(api_key, "together_ai")
                self.logger.info("Together AI API key loaded from config file")
                # Update the UI to reflect the change
                self.provider_combo.setCurrentIndex(0)
            else:
                if not self._show_api_key_dialog():
                    # If user cancels, show error
                    QMessageBox.critical(
                        self,
                        "API Key Required",
                        "An API key is required to use this application. Please restart and enter your API key."
                    )

    def _show_api_key_dialog(self):
        """Show dialog to configure API keys."""
        self.logger.debug("Showing API key dialog")
        dialog = ApiKeyDialog(self, self.config_manager, self.controller.license_manager)
        result = dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            # Save Together AI API key
            together_api_key = dialog.get_together_api_key()
            together_key_saved = False
            if together_api_key:
                self.logger.debug("Saving new Together AI API key to config")
                together_key_saved = self.config_manager.set_api_key(together_api_key, "together_ai")
                if not together_key_saved:
                    self.logger.error("Failed to save Together AI API key to config")
                    QMessageBox.critical(
                        self,
                        "Error",
                        "Failed to save Together AI API key to config. Please try again."
                    )

            # Save Runware AI API key
            runware_api_key = dialog.get_runware_api_key()
            runware_key_saved = False
            if runware_api_key:
                self.logger.debug("Saving new Runware AI API key to config")
                runware_key_saved = self.config_manager.set_api_key(runware_api_key, "runware_ai")
                self.logger.debug(f"Runware AI API key saved: {'*' * 5}{runware_api_key[-5:] if runware_api_key else 'None'}")

                if not runware_key_saved:
                    self.logger.error("Failed to save Runware AI API key to config")
                    QMessageBox.critical(
                        self,
                        "Error",
                        "Failed to save Runware AI API key to config. Please try again."
                    )

            # If neither key was saved successfully, return False
            if (together_api_key and not together_key_saved) or (runware_api_key and not runware_key_saved):
                return False

            # Update the controller with the current provider's API key
            current_provider = self.config_manager.get_setting("default_provider", "together_ai")
            if current_provider == "together_ai" and together_api_key:
                self.controller.update_api_key(together_api_key, "together_ai")
            elif current_provider == "runware_ai" and runware_api_key:
                self.controller.update_api_key(runware_api_key, "runware_ai")

            self.logger.info("API keys saved successfully")
            QMessageBox.information(
                self,
                "API Keys Saved",
                "Your API keys have been saved successfully."
            )

            # Return True to indicate success
            return True
        else:
            self.logger.warning("API key dialog cancelled by user")
            return False

    def _show_api_key_manager(self):
        """Show the comprehensive API key manager dialog."""
        self.logger.debug("Showing API key manager")
        try:
            dialog = ApiKeyManagerDialog(
                parent=self,
                secure_storage=self.config_manager.secure_storage,
                license_manager=self.controller.license_manager
            )
            dialog.exec()

            # After the dialog closes, refresh API clients if needed
            self._refresh_api_clients_after_key_change()

        except Exception as e:
            self.logger.error(f"Error showing API key manager: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to open API key manager: {str(e)}"
            )

    def _refresh_api_clients_after_key_change(self):
        """Refresh API clients after API keys have been changed."""
        try:
            # Get current provider
            current_provider = self.config_manager.get_setting("default_provider", "together_ai")

            # Check if we have an API key for the current provider
            api_key = self.config_manager.get_api_key(current_provider)

            if api_key:
                # Update the controller with the current API key
                self.controller.update_api_key(api_key, current_provider)
                self.logger.info(f"Refreshed API client for {current_provider}")
            else:
                self.logger.warning(f"No API key found for current provider {current_provider}")

        except Exception as e:
            self.logger.error(f"Error refreshing API clients: {e}")

    def _show_license_dialog(self):
        """Show license activation dialog."""
        self.logger.debug("Showing license activation dialog")
        dialog = LicenseActivationDialog(self, self.controller.license_manager)
        result = dialog.exec()

        # Refresh UI after license dialog closes (in case license was activated)
        if result == QDialog.DialogCode.Accepted:
            self._refresh_license_ui()

    def _refresh_license_ui(self):
        """Refresh UI elements based on current license status."""
        try:
            # Update license status banner
            self._update_license_status()

            # Check bulk generation access
            self._check_bulk_generation_access()

            # Refresh provider dropdowns
            self._refresh_provider_dropdowns()

            # Refresh model dropdowns
            self._populate_model_dropdown()
            self._populate_bulk_model_dropdown()

            self.logger.info("UI refreshed after license status change")

        except Exception as e:
            self.logger.error(f"Error refreshing license UI: {e}")

    def _refresh_provider_dropdowns(self):
        """Refresh provider dropdowns based on license status."""
        try:
            license_manager = self.controller.license_manager

            # Clear and repopulate main provider dropdown
            self.provider_combo.blockSignals(True)
            self.provider_combo.clear()

            if license_manager.is_trial_plan():
                self.provider_combo.addItems(["Together AI"])
                self.provider_combo.setCurrentIndex(0)
                self.config_manager.set_setting("default_provider", "together_ai")
            else:
                self.provider_combo.addItems(["Together AI", "Runware AI", "Replicate AI"])
                current_provider = self.config_manager.get_setting("default_provider", "together_ai")
                if current_provider == "together_ai":
                    self.provider_combo.setCurrentIndex(0)
                elif current_provider == "runware_ai":
                    self.provider_combo.setCurrentIndex(1)
                elif current_provider == "replicate_ai":
                    self.provider_combo.setCurrentIndex(2)
                else:
                    self.provider_combo.setCurrentIndex(0)  # Default to Together AI

            self.provider_combo.blockSignals(False)

            # Clear and repopulate bulk provider dropdown
            self.bulk_provider_combo.blockSignals(True)
            self.bulk_provider_combo.clear()

            if license_manager.is_trial_plan():
                self.bulk_provider_combo.addItems(["Together AI"])
                self.bulk_provider_combo.setCurrentIndex(0)
            else:
                self.bulk_provider_combo.addItems(["Together AI", "Runware AI", "Replicate AI"])
                current_provider = self.config_manager.get_setting("default_provider", "together_ai")
                if current_provider == "together_ai":
                    self.bulk_provider_combo.setCurrentIndex(0)
                elif current_provider == "runware_ai":
                    self.bulk_provider_combo.setCurrentIndex(1)
                elif current_provider == "replicate_ai":
                    self.bulk_provider_combo.setCurrentIndex(2)
                else:
                    self.bulk_provider_combo.setCurrentIndex(0)  # Default to Together AI

            self.bulk_provider_combo.blockSignals(False)

        except Exception as e:
            self.logger.error(f"Error refreshing provider dropdowns: {e}")

    def _update_status(self, status):
        """Update the status label.

        Args:
            status (str): Status message.
        """
        self.status_label.setText(status)

    def _on_generation_started(self):
        """Handle generation started event."""
        self.logger.debug("Image generation started")
        self.generate_button.setEnabled(False)
        self.progress_bar.setValue(0)
        self.generation_status_label.setText("Generating image...")

        # Reset the generation completed flag
        self._generation_completed = False

    def _on_generation_progress(self, percentage):
        """Handle generation progress event.

        Args:
            percentage (int): Progress percentage (0-100).
        """
        self.logger.debug(f"Image generation progress: {percentage}%")
        self.progress_bar.setValue(percentage)
        self.generation_status_label.setText(f"Generating image... {percentage}%")

        # Force UI update
        QApplication.processEvents()

    def _on_generation_finished(self):
        """Handle generation finished event."""
        self.logger.debug("Image generation finished")
        self.generate_button.setEnabled(True)
        self.progress_bar.setValue(100)
        self.generation_status_label.setText("Image generation complete")

        # Increment usage tracker after successful generation (only once)
        if not self._generation_completed:
            self._generation_completed = True
            self.usage_tracker.increment_usage()

            # Update license status display to reflect new usage
            self._update_license_status()

    def _display_image(self, pixmap):
        """Display an image.

        Args:
            pixmap (QPixmap): Image to display.
        """
        try:
            self.logger.debug("Displaying generated image")

            # Ensure we're in the main thread
            from PyQt6.QtCore import QThread
            if QThread.currentThread() is not QThread.currentThread():
                self.logger.warning("_display_image called from non-main thread, using QTimer")
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(0, lambda: self._display_image_impl(pixmap))
                return

            # We're in the main thread, proceed with display
            self._display_image_impl(pixmap)

        except Exception as e:
            self.logger.error(f"Error displaying image: {str(e)}", exc_info=True)

    def _display_image_impl(self, pixmap):
        """Implementation of image display that must run in the main thread.

        Args:
            pixmap (QPixmap): Image to display.
        """
        try:
            # Scale the pixmap to fit the available space while maintaining aspect ratio
            label_size = self.image_label.size()
            if label_size.width() > 100 and label_size.height() > 100:  # Ensure label has reasonable size
                scaled_pixmap = pixmap.scaled(
                    label_size,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
            else:
                # Fallback to a reasonable size if label size is not available
                scaled_pixmap = pixmap.scaled(
                    512, 512,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

            # Set the scaled pixmap to the image label
            self.image_label.setPixmap(scaled_pixmap)
            self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.save_button.setEnabled(True)

            # Maintain splitter proportions after image display
            if hasattr(self, 'main_splitter'):
                # Force equal proportions
                total_width = self.main_splitter.width()
                if total_width > 100:  # Ensure splitter has reasonable width
                    equal_width = total_width // 2
                    self.main_splitter.setSizes([equal_width, equal_width])

            self.logger.debug("Image displayed successfully with proper scaling")
        except Exception as e:
            self.logger.error(f"Error in _display_image_impl: {str(e)}", exc_info=True)

    def _update_image_counter_display(self):
        """Update the image counter display in the status bar."""
        try:
            daily_count = self.usage_tracker.get_daily_count()
            daily_limit = self.usage_tracker.get_daily_limit()

            if daily_limit == -1:
                # Unlimited plan
                counter_text = f"Images today: {daily_count}"
                self.image_counter_label.setStyleSheet("""
                    QLabel#imageCounterLabel {
                        font-weight: bold;
                        padding: 2px 8px;
                        margin: 0 10px;
                        border-radius: 3px;
                        background-color: rgba(40, 167, 69, 0.1);
                        color: #28a745;
                    }
                """)
            else:
                # Limited plan
                remaining = max(0, daily_limit - daily_count)
                counter_text = f"Images: {daily_count}/{daily_limit} (remaining: {remaining})"

                # Change color based on usage
                if remaining <= 0:
                    # Limit exceeded - red
                    color_style = """
                        QLabel#imageCounterLabel {
                            font-weight: bold;
                            padding: 2px 8px;
                            margin: 0 10px;
                            border-radius: 3px;
                            background-color: rgba(220, 53, 69, 0.1);
                            color: #dc3545;
                        }
                    """
                elif remaining <= 2:
                    # Warning - orange
                    color_style = """
                        QLabel#imageCounterLabel {
                            font-weight: bold;
                            padding: 2px 8px;
                            margin: 0 10px;
                            border-radius: 3px;
                            background-color: rgba(255, 193, 7, 0.1);
                            color: #ffc107;
                        }
                    """
                else:
                    # Normal - blue
                    color_style = """
                        QLabel#imageCounterLabel {
                            font-weight: bold;
                            padding: 2px 8px;
                            margin: 0 10px;
                            border-radius: 3px;
                            background-color: rgba(0, 123, 255, 0.1);
                            color: #007bff;
                        }
                    """

                self.image_counter_label.setStyleSheet(color_style)

            self.image_counter_label.setText(counter_text)

        except Exception as e:
            self.logger.error(f"Error updating image counter display: {e}")
            self.image_counter_label.setText("Images: Error")

    def _on_usage_updated(self, daily_count, daily_limit):
        """Handle usage updated signal from usage tracker.

        Args:
            daily_count (int): Current daily count
            daily_limit (int): Daily limit (-1 for unlimited)
        """
        self.logger.debug(f"Usage updated: {daily_count}/{daily_limit if daily_limit != -1 else 'unlimited'}")
        self._update_image_counter_display()

    def _on_limit_exceeded(self, error_message):
        """Handle limit exceeded signal from usage tracker.

        Args:
            error_message (str): Error message about limit being exceeded
        """
        self.logger.warning(f"Daily limit exceeded: {error_message}")
        self._update_image_counter_display()

        # Show error message to user
        QMessageBox.warning(
            self,
            "Daily Limit Reached",
            error_message
        )

    def _on_limit_warning(self, warning_message, remaining_count):
        """Handle limit warning signal from usage tracker.

        Args:
            warning_message (str): Warning message
            remaining_count (int): Number of images remaining
        """
        self.logger.info(f"Usage warning: {warning_message}")
        self._update_image_counter_display()

        # Show warning message to user
        QMessageBox.information(
            self,
            "Approaching Daily Limit",
            warning_message
        )

    def _on_usage_reset(self):
        """Handle usage reset signal from usage tracker."""
        self.logger.info("Daily usage count has been reset")
        self._update_image_counter_display()
        self._update_license_status()


    def resizeEvent(self, event):
        """Handle window resize events to maintain splitter proportions."""
        super().resizeEvent(event)

        # Maintain equal splitter proportions on resize
        if hasattr(self, 'main_splitter'):
            total_width = self.main_splitter.width()
            if total_width > 100:
                equal_width = total_width // 2
                self.main_splitter.setSizes([equal_width, equal_width])

    def _show_error(self, message):
        """Show an error message.

        Args:
            message (str): Error message.
        """
        QMessageBox.critical(self, "Error", message)

    def _on_prompt_changed(self):
        """Handle prompt text changes."""
        # Auto-generate feature has been removed
        # Just pass the text to the controller for future use
        self.controller.on_prompt_changed(self.prompt_input.text())

    def _on_provider_changed(self, index):
        """Handle provider selection change.

        Args:
            index (int): Index of the selected provider.
        """
        if index == 0:
            provider = "together_ai"
            provider_name = "Together AI"
        elif index == 1:
            provider = "runware_ai"
            provider_name = "Runware AI"
        elif index == 2:
            provider = "replicate_ai"
            provider_name = "Replicate AI"
        else:
            provider = "together_ai"
            provider_name = "Together AI"
        self.logger.info(f"Switching to provider: {provider}")

        # Check if API key exists for the selected provider
        api_key = self.config_manager.get_api_key(provider)
        if not api_key:
            self.logger.warning(f"No API key found for {provider_name}")
            # Show API key dialog
            QMessageBox.warning(
                self,
                "API Key Required",
                f"No API key found for {provider_name}. Please enter your API key."
            )

            # Show API key manager
            reply = QMessageBox.question(
                self,
                "API Key Required",
                f"No API key found for {provider_name}. Would you like to configure it now?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )

            if reply == QMessageBox.StandardButton.Yes:
                self._show_api_key_manager()

                # Double-check if API key was entered
                api_key = self.config_manager.get_api_key(provider)
                if not api_key:
                    self.logger.warning(f"No API key configured for {provider_name}")
                    # Revert to previous provider (default to Together AI)
                    self.provider_combo.setCurrentIndex(0)  # Together AI
                    return
            else:
                # User declined, revert to previous provider (default to Together AI)
                self.provider_combo.setCurrentIndex(0)  # Together AI
                return

        # Switch the provider in the controller
        if self.controller.switch_provider(provider):
            # Update the bulk generation tab provider dropdown (without triggering its event)
            self.bulk_provider_combo.blockSignals(True)
            self.bulk_provider_combo.setCurrentIndex(index)
            self.bulk_provider_combo.blockSignals(False)

            # Update both model dropdowns
            self._populate_model_dropdown()
            self._populate_bulk_model_dropdown()

            # Sync the model selection between tabs if possible
            if self.model_combo.count() > 0 and self.bulk_model_combo.count() > 0:
                # Get the first model name from the main dropdown
                first_model_name = self.model_combo.itemText(0)

                # Find and select the same model in the bulk dropdown
                bulk_index = self.bulk_model_combo.findText(first_model_name)
                if bulk_index >= 0:
                    self.bulk_model_combo.blockSignals(True)
                    self.bulk_model_combo.setCurrentIndex(bulk_index)
                    self.bulk_model_combo.blockSignals(False)
                    self.logger.debug(f"Synchronized model selection: {first_model_name}")

    def _on_model_changed(self, model_name):
        """Handle model selection change in the main tab.

        Args:
            model_name (str): Name of the selected model.
        """
        if model_name:
            self.logger.debug(f"Model changed to: {model_name}")

            # Find the index of the model in the bulk model dropdown
            index = self.bulk_model_combo.findText(model_name)
            if index >= 0:
                # Update the bulk model dropdown (without triggering its event)
                self.bulk_model_combo.blockSignals(True)
                self.bulk_model_combo.setCurrentIndex(index)
                self.bulk_model_combo.blockSignals(False)
                self.logger.debug(f"Synchronized bulk tab model to: {model_name}")

            # Adjust steps based on the selected model
            model_id = self.model_combo.currentData()
            # Check for FLUX Schnell model from any provider
            if (model_id and "runware:100@1" in model_id) or \
               (model_id and "FLUX.1-schnell" in model_id) or \
               (model_id and "flux-schnell" in model_id):
                # Set steps to 4 for FLUX Schnell
                self._set_steps_range(1, 10, 4)
                self.logger.debug(f"Set steps to 4 for FLUX Schnell model: {model_id}")
            else:
                # Set steps to 28 for all other models with max 50
                self._set_steps_range(1, 50, 28)
                self.logger.debug(f"Set steps to 28 for model: {model_name}")

    def _on_bulk_provider_changed(self, index):
        """Handle provider selection change in the bulk generation tab.

        Args:
            index (int): Index of the selected provider.
        """
        if index == 0:
            provider = "together_ai"
            provider_name = "Together AI"
        elif index == 1:
            provider = "runware_ai"
            provider_name = "Runware AI"
        elif index == 2:
            provider = "replicate_ai"
            provider_name = "Replicate AI"
        else:
            provider = "together_ai"
            provider_name = "Together AI"
        self.logger.info(f"Switching to provider in bulk tab: {provider}")

        # Check if API key exists for the selected provider
        api_key = self.config_manager.get_api_key(provider)
        if not api_key:
            self.logger.warning(f"No API key found for {provider_name}")
            # Show API key dialog
            QMessageBox.warning(
                self,
                "API Key Required",
                f"No API key found for {provider_name}. Please enter your API key."
            )

            # Show API key dialog and check if it was successful
            if not self._show_api_key_dialog():
                self.logger.warning(f"API key dialog cancelled for {provider_name}")
                # Revert to previous provider (default to Together AI)
                self.bulk_provider_combo.setCurrentIndex(0)  # Together AI
                return

            # Double-check if API key was entered
            api_key = self.config_manager.get_api_key(provider)
            if not api_key:
                self.logger.warning(f"No API key entered for {provider_name}")
                # Revert to previous provider (default to Together AI)
                self.bulk_provider_combo.setCurrentIndex(0)  # Together AI
                return

        # Switch the provider in the controller
        if self.controller.switch_provider(provider):
            # Update the main tab provider dropdown (without triggering its event)
            self.provider_combo.blockSignals(True)
            self.provider_combo.setCurrentIndex(index)
            self.provider_combo.blockSignals(False)

            # Update both model dropdowns
            self._populate_model_dropdown()
            self._populate_bulk_model_dropdown()

            # Sync the model selection between tabs if possible
            if self.bulk_model_combo.count() > 0 and self.model_combo.count() > 0:
                # Get the first model name from the bulk dropdown
                first_model_name = self.bulk_model_combo.itemText(0)

                # Find and select the same model in the main dropdown
                main_index = self.model_combo.findText(first_model_name)
                if main_index >= 0:
                    self.model_combo.blockSignals(True)
                    self.model_combo.setCurrentIndex(main_index)
                    self.model_combo.blockSignals(False)
                    self.logger.debug(f"Synchronized model selection: {first_model_name}")

    def _on_bulk_model_changed(self, model_name):
        """Handle model selection change in the bulk generation tab.

        Args:
            model_name (str): Name of the selected model.
        """
        if model_name:
            self.logger.debug(f"Bulk tab model changed to: {model_name}")

            # Find the index of the model in the main model dropdown
            index = self.model_combo.findText(model_name)
            if index >= 0:
                # Update the main model dropdown (without triggering its event)
                self.model_combo.blockSignals(True)
                self.model_combo.setCurrentIndex(index)
                self.model_combo.blockSignals(False)
                self.logger.debug(f"Synchronized main tab model to: {model_name}")

            # Adjust steps based on the selected model
            model_id = self.bulk_model_combo.currentData()
            # Check for FLUX Schnell model from any provider
            if (model_id and "runware:100@1" in model_id) or \
               (model_id and "FLUX.1-schnell" in model_id) or \
               (model_id and "flux-schnell" in model_id):
                # Set steps to 4 for FLUX Schnell
                self._set_steps_range(1, 10, 4)
                self.logger.debug(f"Set steps to 4 for FLUX Schnell model: {model_id}")
            else:
                # Set steps to 28 for all other models with max 50
                self._set_steps_range(1, 50, 28)
                self.logger.debug(f"Set steps to 28 for model: {model_name}")

    def _on_style_changed(self, style_name):
        """Handle style selection change in the main tab.

        Args:
            style_name (str): Name of the selected style.
        """
        if style_name:
            self.logger.debug(f"Style changed to: {style_name}")

            # Save the selected style to config
            self.config_manager.set_setting("default_image_style", style_name)

            # Sync the bulk style dropdown (without triggering its event)
            bulk_index = self.bulk_style_combo.findText(style_name)
            if bulk_index >= 0:
                self.bulk_style_combo.blockSignals(True)
                self.bulk_style_combo.setCurrentIndex(bulk_index)
                self.bulk_style_combo.blockSignals(False)
                self.logger.debug(f"Synchronized bulk tab style to: {style_name}")

    def _on_bulk_style_changed(self, style_name):
        """Handle style selection change in the bulk generation tab.

        Args:
            style_name (str): Name of the selected style.
        """
        if style_name:
            self.logger.debug(f"Bulk tab style changed to: {style_name}")

            # Save the selected style to config
            self.config_manager.set_setting("default_image_style", style_name)

            # Sync the main style dropdown (without triggering its event)
            main_index = self.style_combo.findText(style_name)
            if main_index >= 0:
                self.style_combo.blockSignals(True)
                self.style_combo.setCurrentIndex(main_index)
                self.style_combo.blockSignals(False)
                self.logger.debug(f"Synchronized main tab style to: {style_name}")

    def _populate_model_dropdown(self):
        """Populate the model dropdown based on current provider from config."""
        # Clear the current items
        self.model_combo.clear()

        # Get the current provider from the dropdown (not config)
        provider_index = self.provider_combo.currentIndex()
        if provider_index == 0:
            provider = "together_ai"
        elif provider_index == 1:
            provider = "runware_ai"
        elif provider_index == 2:
            provider = "replicate_ai"
        else:
            provider = "together_ai"
        self.logger.debug(f"Populating model dropdown for provider: {provider} (index: {provider_index})")
        self.logger.debug(f"Populating model dropdown for provider: {provider}")

        # Get models for the current provider from config
        models = []
        try:
            models = self.config_manager.get_provider_models(provider)
            self.logger.debug(f"Got {len(models)} models from get_provider_models for {provider}")
        except Exception as e:
            self.logger.error(f"Error getting provider models: {e}")
            models = []

        # If no models found, use fallback logic
        if not models:
            self.logger.debug(f"No models found for {provider}, using fallback logic")
            if provider == "runware_ai":
                try:
                    old_models = self.config_manager.get_runware_models()
                    # Convert old format to new format
                    models = [
                        {
                            "id": model["id"],
                            "name": model["name"],
                            "default_steps": 4 if "schnell" in model["name"].lower() else 28,
                            "max_steps": 50
                        }
                        for model in old_models
                    ]
                    self.logger.debug(f"Converted {len(models)} Runware models from old format")
                except Exception as e:
                    self.logger.error(f"Error getting Runware models: {e}")
                    models = []
            elif provider == "replicate_ai":
                # Replicate AI default models
                models = [
                    {
                        "id": "black-forest-labs/flux-dev",
                        "name": "FLUX Dev",
                        "default_steps": 28,
                        "max_steps": 50,
                        "supports_aspect_ratio": True
                    },
                    {
                        "id": "black-forest-labs/flux-schnell",
                        "name": "FLUX Schnell",
                        "default_steps": 4,
                        "max_steps": 4,
                        "supports_aspect_ratio": True
                    }
                ]
                self.logger.debug("Using Replicate AI default models")
            else:
                # Together AI default model
                models = [
                    {
                        "id": "black-forest-labs/FLUX.1-schnell-Free",
                        "name": "FLUX.1-schnell-Free",
                        "default_steps": 4,
                        "max_steps": 4
                    }
                ]
                self.logger.debug("Using Together AI default model")

        # Filter models based on license restrictions
        license_manager = self.controller.license_manager
        allowed_models = license_manager.get_allowed_models()
        if allowed_models is not None:  # None means no restrictions (Pro user)
            models = [model for model in models if model["id"] in allowed_models]
            self.logger.debug(f"Filtered to {len(models)} models based on license restrictions")

        # Populate the dropdown
        for model in models:
            self.model_combo.addItem(model["name"], model["id"])

        if models:
            self.logger.debug(f"Populated model dropdown with {len(models)} models for {provider}")

            # Set appropriate steps range based on the first model
            first_model = models[0]
            default_steps = first_model.get("default_steps", 28)
            max_steps = first_model.get("max_steps", 50)
            self._set_steps_range(1, max_steps, default_steps)
            self.logger.debug(f"Set steps to {default_steps} (max {max_steps}) for model: {first_model['name']}")
        else:
            self.model_combo.addItem("No models available", None)
            self.logger.warning(f"No models available for provider: {provider}")

    def _populate_bulk_model_dropdown(self):
        """Populate the bulk model dropdown based on current provider from config."""
        # Clear the current items
        self.bulk_model_combo.clear()

        # Get the current provider from the dropdown (not config)
        provider_index = self.provider_combo.currentIndex()
        if provider_index == 0:
            provider = "together_ai"
        elif provider_index == 1:
            provider = "runware_ai"
        elif provider_index == 2:
            provider = "replicate_ai"
        else:
            provider = "together_ai"
        self.logger.debug(f"Populating model dropdown for provider: {provider} (index: {provider_index})")

        # Get models for the current provider from config
        try:
            models = self.config_manager.get_provider_models(provider)
        except AttributeError:
            # Fallback to old method for backward compatibility
            if provider == "runware_ai":
                models = self.config_manager.get_runware_models()
                # Convert old format to new format
                models = [
                    {
                        "id": model["id"],
                        "name": model["name"],
                        "default_steps": 4 if "schnell" in model["name"].lower() else 28,
                        "max_steps": 50
                    }
                    for model in models
                ]
            elif provider == "replicate_ai":
                # Replicate AI default models
                models = [
                    {
                        "id": "black-forest-labs/flux-dev",
                        "name": "FLUX Dev",
                        "default_steps": 28,
                        "max_steps": 50,
                        "supports_aspect_ratio": True
                    },
                    {
                        "id": "black-forest-labs/flux-schnell",
                        "name": "FLUX Schnell",
                        "default_steps": 4,
                        "max_steps": 4,
                        "supports_aspect_ratio": True
                    }
                ]
            else:
                # Together AI default model
                models = [
                    {
                        "id": "black-forest-labs/FLUX.1-schnell-Free",
                        "name": "FLUX.1-schnell-Free",
                        "default_steps": 4,
                        "max_steps": 4
                    }
                ]

        # Filter models based on license restrictions
        license_manager = self.controller.license_manager
        allowed_models = license_manager.get_allowed_models()
        if allowed_models is not None:  # None means no restrictions (Pro user)
            models = [model for model in models if model["id"] in allowed_models]
            self.logger.debug(f"Filtered bulk models to {len(models)} based on license restrictions")

        # Populate the dropdown
        for model in models:
            self.bulk_model_combo.addItem(model["name"], model["id"])

        if models:
            self.logger.debug(f"Populated bulk model dropdown with {len(models)} models for {provider}")

            # Set appropriate steps range based on the first model
            first_model = models[0]
            default_steps = first_model.get("default_steps", 28)
            max_steps = first_model.get("max_steps", 50)
            self._set_steps_range(1, max_steps, default_steps)
            self.logger.debug(f"Set steps to {default_steps} (max {max_steps}) for model: {first_model['name']} (bulk tab)")
        else:
            self.bulk_model_combo.addItem("No models available", None)

    def _populate_size_options(self):
        """Populate the size options based on the selected resolution type."""
        # Clear current items
        self.size_combo.clear()

        # Get the selected resolution type
        res_type_index = self.res_type_combo.currentIndex()

        # Populate based on resolution type
        if res_type_index == 0:  # Square (1:1)
            self.size_combo.addItems([
                "512 x 512",
                "640 x 640",
                "768 x 768",
                "896 x 896",
                "1024 x 1024",
                "1152 x 1152",
                "1280 x 1280",
                "1408 x 1408"
            ])
            self.size_combo.setCurrentIndex(4)  # Default to 1024x1024
        elif res_type_index == 1:  # Landscape (16:9)
            self.size_combo.addItems([
                "512 x 320",
                "640 x 384",
                "768 x 448",
                "896 x 512",
                "1024 x 576",
                "1152 x 640",
                "1280 x 704",
                "1408 x 768"
            ])
            self.size_combo.setCurrentIndex(6)  # Default to 1280x704
        elif res_type_index == 2:  # Portrait (9:16)
            self.size_combo.addItems([
                "320 x 512",
                "384 x 640",
                "448 x 768",
                "512 x 896",
                "576 x 1024",
                "640 x 1152",
                "704 x 1280",
                "768 x 1408"
            ])
            self.size_combo.setCurrentIndex(6)  # Default to 704x1280
        # For Custom, we don't need to add items to the combo box

    def _populate_bulk_size_options(self):
        """Populate the bulk size options based on the selected resolution type."""
        # Clear current items
        self.bulk_size_combo.clear()

        # Get the selected resolution type
        res_type_index = self.bulk_res_type_combo.currentIndex()

        # Populate based on resolution type
        if res_type_index == 0:  # Square (1:1)
            self.bulk_size_combo.addItems([
                "512 x 512",
                "640 x 640",
                "768 x 768",
                "896 x 896",
                "1024 x 1024",
                "1152 x 1152",
                "1280 x 1280",
                "1408 x 1408"
            ])
            self.bulk_size_combo.setCurrentIndex(4)  # Default to 1024x1024
        elif res_type_index == 1:  # Landscape (16:9)
            self.bulk_size_combo.addItems([
                "512 x 320",
                "640 x 384",
                "768 x 448",
                "896 x 512",
                "1024 x 576",
                "1152 x 640",
                "1280 x 704",
                "1408 x 768"
            ])
            self.bulk_size_combo.setCurrentIndex(6)  # Default to 1280x704
        elif res_type_index == 2:  # Portrait (9:16)
            self.bulk_size_combo.addItems([
                "320 x 512",
                "384 x 640",
                "448 x 768",
                "512 x 896",
                "576 x 1024",
                "640 x 1152",
                "704 x 1280",
                "768 x 1408"
            ])
            self.bulk_size_combo.setCurrentIndex(6)  # Default to 704x1280
        # For Custom, we don't need to add items to the combo box

    def _on_resolution_type_changed(self, index):
        """Handle resolution type change."""
        # Update size options
        self._populate_size_options()

        # Show/hide custom dimensions
        self._toggle_custom_dimensions(index == 3)  # Show if Custom is selected

    def _on_bulk_resolution_type_changed(self, index):
        """Handle bulk resolution type change."""
        # Update size options
        self._populate_bulk_size_options()

        # Show/hide custom dimensions
        self._toggle_bulk_custom_dimensions(index == 3)  # Show if Custom is selected

    def _toggle_custom_dimensions(self, show):
        """Show or hide custom dimension controls."""
        # Show/hide size combo box (opposite of custom dimensions)
        self.size_combo.setVisible(not show)

        # Find the size label and hide/show it
        for label in self.size_combo.parentWidget().findChildren(QLabel):
            if label.text() == "Image Size:":
                label.setVisible(not show)
                break

        # Show/hide custom dimension controls
        for widget in [self.width_label, self.width_spin, self.height_label, self.height_spin]:
            widget.setVisible(show)

    def _toggle_bulk_custom_dimensions(self, show):
        """Show or hide bulk custom dimension controls."""
        # Show/hide size combo box (opposite of custom dimensions)
        self.bulk_size_combo.setVisible(not show)

        # Find the size label and hide/show it
        for label in self.bulk_size_combo.parentWidget().findChildren(QLabel):
            if label.text() == "Image Size:":
                label.setVisible(not show)
                break

        # Show/hide custom dimension controls
        for widget in [self.bulk_width_label, self.bulk_width_spin, self.bulk_height_label, self.bulk_height_spin]:
            widget.setVisible(show)

    def _adjust_width_to_multiple_of_64(self, value):
        """Adjust width to be a multiple of 64."""
        adjusted_value = ((value + 32) // 64) * 64  # Round to nearest multiple of 64
        if adjusted_value != value:
            self.width_spin.setValue(adjusted_value)

    def _adjust_height_to_multiple_of_64(self, value):
        """Adjust height to be a multiple of 64."""
        adjusted_value = ((value + 32) // 64) * 64  # Round to nearest multiple of 64
        if adjusted_value != value:
            self.height_spin.setValue(adjusted_value)

    def _adjust_bulk_width_to_multiple_of_64(self, value):
        """Adjust bulk width to be a multiple of 64."""
        adjusted_value = ((value + 32) // 64) * 64  # Round to nearest multiple of 64
        if adjusted_value != value:
            self.bulk_width_spin.setValue(adjusted_value)

    def _adjust_bulk_height_to_multiple_of_64(self, value):
        """Adjust bulk height to be a multiple of 64."""
        adjusted_value = ((value + 32) // 64) * 64  # Round to nearest multiple of 64
        if adjusted_value != value:
            self.bulk_height_spin.setValue(adjusted_value)

    def _get_image_dimensions(self):
        """Get the image dimensions based on the current resolution settings."""
        res_type_index = self.res_type_combo.currentIndex()

        if res_type_index == 3:  # Custom
            # Get dimensions from spin boxes
            width = self.width_spin.value()
            height = self.height_spin.value()
            return width, height
        else:
            # Get dimensions from combo box
            size_text = self.size_combo.currentText()
            width, height = map(int, size_text.split(" x "))
            return width, height

    def _get_bulk_image_dimensions(self):
        """Get the bulk image dimensions based on the current resolution settings."""
        res_type_index = self.bulk_res_type_combo.currentIndex()

        if res_type_index == 3:  # Custom
            # Get dimensions from spin boxes
            width = self.bulk_width_spin.value()
            height = self.bulk_height_spin.value()
            return width, height
        else:
            # Get dimensions from combo box
            size_text = self.bulk_size_combo.currentText()
            width, height = map(int, size_text.split(" x "))
            return width, height

    def _generate_image(self):
        """Generate an image based on the current settings."""
        # Check if user can generate an image (daily limit check)
        can_generate, error_message = self.usage_tracker.can_generate_image()
        if not can_generate:
            self.logger.warning(f"Image generation blocked: {error_message}")
            QMessageBox.warning(
                self,
                "Daily Limit Reached",
                error_message + "\n\nUpgrade to Pro for unlimited image generation."
            )
            return

        prompt = self.prompt_input.text().strip()
        if not prompt:
            QMessageBox.warning(self, "Error", "Please enter a prompt.")
            return

        # Get the selected style and modify prompt if needed
        selected_style = self.style_combo.currentText()
        styled_prompt = self._apply_style_to_prompt(prompt, selected_style)

        steps = self.steps_spin.value()
        seed = self.seed_spin.value() if self.seed_spin.value() >= 0 else None

        # Get the selected model ID if available
        model_id = None
        provider = self.config_manager.get_setting("default_provider", "together_ai")

        if provider == "together_ai":
            # Together AI only has one free model, no need to specify model_id
            model_id = None
            self.logger.debug("Using Together AI - no model_id needed (uses default free model)")
        else:
            # For Runware AI and Replicate AI, get the model ID from the dropdown
            if self.model_combo.currentData():
                model_id = self.model_combo.currentData()
                self.logger.debug(f"Using {provider} model ID: {model_id}")

        # Get dimensions based on resolution type
        width, height = self._get_image_dimensions()
        size_text = f"{width} x {height}"

        self.logger.info(f"Generating image with prompt: '{styled_prompt}', size: {size_text}, steps: {steps}, seed: {seed}, model: {model_id}, style: {selected_style}")
        self.controller.generate_image(styled_prompt, size_text, steps, seed, model_id)

    def _apply_style_to_prompt(self, prompt, style):
        """Apply the selected style to the prompt.

        Args:
            prompt (str): Original prompt text.
            style (str): Selected style name.

        Returns:
            str: Modified prompt with style applied.
        """
        if style == "None" or not style:
            return prompt

        # Define style modifiers
        style_modifiers = {
            "Photorealistic": ", photorealistic, highly detailed, professional photography, sharp focus",
            "Digital Art": ", digital art, concept art, trending on artstation, highly detailed",
            "Oil Painting": ", oil painting, classical art style, painterly, brush strokes, fine art",
            "Watercolor": ", watercolor painting, soft colors, flowing, artistic, traditional media",
            "Anime/Manga": ", anime style, manga art, cel shading, vibrant colors, Japanese animation",
            "Sketch/Drawing": ", pencil sketch, hand drawn, artistic sketch, black and white, detailed drawing",
            "Cinematic": ", cinematic lighting, dramatic, movie scene, professional cinematography, epic",
            "Fantasy Art": ", fantasy art, magical, mystical, ethereal, fantasy illustration, enchanted",
            "Abstract": ", abstract art, modern art, artistic interpretation, creative, non-representational",
            "Vintage/Retro": ", vintage style, retro aesthetic, nostalgic, classic, old-fashioned"
        }

        modifier = style_modifiers.get(style, "")
        styled_prompt = prompt + modifier

        self.logger.debug(f"Applied style '{style}' to prompt: '{prompt}' -> '{styled_prompt}'")
        return styled_prompt

    def _save_image(self):
        """Save the current image."""
        # Get the default save directory from the controller
        default_dir = os.path.join(self.controller.images_dir, "image.png")

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Image",
            default_dir,
            "Images (*.png *.jpg)"
        )

        if file_path:
            self.controller.save_image(file_path)
        else:
            # If user cancels the dialog, save with auto-generated filename
            self.logger.debug("Save dialog cancelled, using auto-generated filename")
            self.controller.save_image()

    def _show_about_dialog(self):
        """Show about dialog."""
        # Create a custom about dialog with logo
        about_box = QMessageBox(self)
        about_box.setWindowTitle("About Azanx Bulk AI Images")
        about_box.setTextFormat(Qt.TextFormat.RichText)

        # Load the logo
        logo = QPixmap("ui/resources/app-logo.svg")
        if not logo.isNull():
            # Scale the logo to a reasonable size
            logo = logo.scaled(128, 128, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            about_box.setIconPixmap(logo)

        # Set the about text
        about_box.setText(
            "Azanx Bulk AI Images<br><br>"
            "A modern, professional desktop application for generating AI images "
            "using the Together AI FLUX.1-schnell-Free model.<br><br>"
            "Version: 1.0.0<br>"
            "© 2024 Azanx<br><br>"
            "Made with love in Pakistan"
        )

        # Show the dialog
        about_box.exec()

    def _browse_prompts_file(self):
        """Open file dialog to select a text file with prompts."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Prompts File",
            "",
            "Text Files (*.txt)"
        )

        if file_path:
            self.logger.debug(f"Selected prompts file: {file_path}")
            self.file_path_input.setText(file_path)
            self.bulk_generate_button.setEnabled(True)

            # Count prompts in the file
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    prompts = [line.strip() for line in f if line.strip()]
                    count = len(prompts)
                    self.bulk_status_label.setText(f"Ready to generate {count} images")
                    self.bulk_progress_bar.setMaximum(count)
            except Exception as e:
                self.logger.error(f"Error reading prompts file: {str(e)}")
                self.error_occurred.emit(f"Error reading prompts file: {str(e)}")

    def _browse_output_folder(self):
        """Open folder dialog to select output directory."""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            "Select Output Folder",
            str(self.controller.images_dir)
        )

        if folder_path:
            self.logger.debug(f"Selected output folder: {folder_path}")
            self.output_folder_input.setText(folder_path)

    def _start_bulk_generation(self):
        """Start bulk image generation process."""
        # Get the prompts file path
        prompts_file = self.file_path_input.text()
        if not prompts_file:
            self.error_occurred.emit("Please select a prompts file first")
            return

        # Get output folder (or use default)
        output_folder = self.output_folder_input.text()

        # Get generation settings
        width, height = self._get_bulk_image_dimensions()
        size_text = f"{width} x {height}"
        steps = self.bulk_steps_spin.value()

        # Count prompts to estimate time
        try:
            with open(prompts_file, 'r', encoding='utf-8') as f:
                prompts = [line.strip() for line in f if line.strip()]
                count = len(prompts)

                # Get the current provider
                provider = self.config_manager.get_setting("default_provider", "together_ai")

                # Calculate estimated time based on provider
                if provider == "together_ai":
                    # ~7 seconds per image for Together AI (9 images per minute)
                    seconds_per_image = 7
                else:
                    # 6 seconds per image for other providers
                    seconds_per_image = 6

                estimated_minutes = (count * seconds_per_image) // 60
                estimated_seconds = (count * seconds_per_image) % 60
                time_estimate = f"{estimated_minutes} minutes and {estimated_seconds} seconds"

                # Show confirmation dialog with time estimate
                confirm = QMessageBox.question(
                    self,
                    "Confirm Bulk Generation",
                    f"You are about to generate {count} images.\n\n"
                    f"With the current provider's rate limit "
                    f"({9 if provider == 'together_ai' else 10} images/minute), "
                    f"this will take approximately {time_estimate}.\n\n"
                    "Do you want to continue?",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if confirm != QMessageBox.StandardButton.Yes:
                    self.logger.info("Bulk generation cancelled by user")
                    return

        except Exception as e:
            self.logger.error(f"Error reading prompts file: {str(e)}")
            self.error_occurred.emit(f"Error reading prompts file: {str(e)}")
            return

        # Get the selected model ID from the bulk model dropdown
        model_id = None
        provider = self.config_manager.get_setting("default_provider", "together_ai")

        if provider == "together_ai":
            # Together AI only has one free model, no need to specify model_id
            model_id = None
            self.logger.debug("Using Together AI - no model_id needed (uses default free model)")
        else:
            # For Runware AI and Replicate AI, get the model ID from the bulk model dropdown
            if self.bulk_model_combo.currentData():
                model_id = self.bulk_model_combo.currentData()
                self.logger.debug(f"Using {provider} model ID from bulk dropdown: {model_id}")
            else:
                self.logger.warning(f"No {provider} model selected in bulk dropdown")

        # Get the selected style for bulk generation
        selected_style = self.bulk_style_combo.currentText()

        # Start bulk generation
        self.logger.info(f"Starting bulk generation with file: {prompts_file}, size: {size_text}, steps: {steps}, model: {model_id}, style: {selected_style}")
        self.controller.start_bulk_generation(prompts_file, size_text, steps, output_folder, model_id, selected_style)

    def _on_bulk_generation_started(self, total_prompts):
        """Handle bulk generation started event.

        Args:
            total_prompts (int): Total number of prompts to process.
        """
        self.logger.debug(f"Bulk generation started with {total_prompts} prompts")

        # Reset bulk progress tracking
        self._last_bulk_progress = 0

        # Disable the generate button
        self.bulk_generate_button.setEnabled(False)

        # Update status label
        self.bulk_status_label.setText(f"Bulk generation in progress (0/{total_prompts})...")

        # Reset and configure progress bar
        self.bulk_progress_bar.setValue(0)
        self.bulk_progress_bar.setMaximum(total_prompts)
        self.bulk_progress_bar.setVisible(True)
        self.bulk_progress_bar.repaint()

        # Reset current prompt label
        self.current_prompt_label.setText("Preparing to process prompts...")
        self.current_prompt_label.setVisible(True)

        # Force UI update - process all pending events
        QApplication.processEvents(QEventLoop.ProcessEventsFlag.AllEvents)

    def _on_bulk_generation_progress(self, current, total):
        """Handle bulk generation progress event.

        Args:
            current (int): Current prompt index (1-based).
            total (int): Total number of prompts.
        """
        self.logger.debug(f"Bulk generation progress: {current}/{total}")

        # Track usage for each completed image in bulk generation
        # Only increment if we've completed a new image
        if not hasattr(self, '_last_bulk_progress'):
            self._last_bulk_progress = 0

        if current > self._last_bulk_progress:
            # We've completed additional images
            images_completed = current - self._last_bulk_progress
            for _ in range(images_completed):
                self.usage_tracker.increment_usage()
            self._last_bulk_progress = current

        # Update progress bar - ensure it's within range
        progress_value = min(current, total)
        self.bulk_progress_bar.setValue(progress_value)

        # Calculate percentage
        percentage = int((progress_value / total) * 100) if total > 0 else 0

        # Update status label with percentage
        self.bulk_status_label.setText(f"Bulk generation in progress: {progress_value}/{total} ({percentage}%)")

        # Make sure the progress bar is visible
        self.bulk_progress_bar.setVisible(True)
        self.bulk_progress_bar.repaint()

        # Force UI update - process all pending events
        QApplication.processEvents(QEventLoop.ProcessEventsFlag.AllEvents)

    def _on_bulk_prompt_processing(self, prompt, index, total):
        """Handle bulk prompt processing event.

        Args:
            prompt (str): Current prompt being processed.
            index (int): Current prompt index (0-based).
            total (int): Total number of prompts.
        """
        self.logger.debug(f"Processing prompt {index+1}/{total}: {prompt}")
        self.current_prompt_label.setText(f"Processing ({index+1}/{total}): {prompt}")

        # Make sure the label is visible
        self.current_prompt_label.setVisible(True)
        self.current_prompt_label.repaint()

        # Update progress bar to show current position
        progress_value = index + 1
        self.bulk_progress_bar.setValue(progress_value)

        # Calculate percentage
        percentage = int((progress_value / total) * 100) if total > 0 else 0

        # Update status label with percentage
        self.bulk_status_label.setText(f"Bulk generation in progress: {progress_value}/{total} ({percentage}%)")

        # Force UI update - process all pending events
        QApplication.processEvents(QEventLoop.ProcessEventsFlag.AllEvents)

    def _on_bulk_generation_finished(self, output_folder):
        """Handle bulk generation finished event.

        Args:
            output_folder (str): Path to the output folder.
        """
        self.logger.info(f"Bulk generation finished. Images saved to: {output_folder}")

        # Update license status display to reflect new usage
        self._update_license_status()

        # Re-enable the generate button
        self.bulk_generate_button.setEnabled(True)

        # Set progress bar to 100%
        self.bulk_progress_bar.setValue(self.bulk_progress_bar.maximum())
        self.bulk_progress_bar.repaint()

        # Update status labels
        if output_folder:
            self.bulk_status_label.setText(f"Bulk generation completed. Images saved to: {output_folder}")
            self.bulk_status_label.repaint()

            # Show a message box to notify the user
            QMessageBox.information(
                self,
                "Bulk Generation Complete",
                f"All images have been generated and saved to:\n{output_folder}"
            )
        else:
            self.bulk_status_label.setText("Bulk generation completed with errors.")
            self.bulk_status_label.repaint()

        self.current_prompt_label.setText("No generation in progress")
        self.current_prompt_label.repaint()

        # Force UI update - process all pending events
        QApplication.processEvents(QEventLoop.ProcessEventsFlag.AllEvents)

# End of MainWindow class
