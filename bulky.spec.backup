# -*- mode: python ; coding: utf-8 -*-
import os
from PyInstaller.utils.hooks import collect_data_files

# Add data files
datas = [
    ('config.json', '.'),
    ('test_prompts.txt', '.'),
    ('ui/resources/app-logo.svg', 'ui/resources'),
    ('ui/resources/splash-screen.svg', 'ui/resources'),
    ('ui/resources/splash-screen-backup.svg', 'ui/resources'),
]

# Create generated_images directory if it doesn't exist
if not os.path.exists('generated_images'):
    os.makedirs('generated_images')

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.QtSvg',  # Added for SVG support
        'runware',
        'PIL',
        'PIL.Image',
        'requests'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Azanx Bulk AI Images',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='ui/resources/app-logo.svg',
)
