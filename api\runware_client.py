import asyncio
import base64
import uuid
from io import BytesIO
from PIL import Image
import json
import os
import requests
from config_manager import Config<PERSON>anager
from logger import get_logger

class RunwareAIClient:
    """Client for interacting with Runware AI API for image generation."""

    def __init__(self, api_key=None, config_manager=None):
        """Initialize the Runware AI client.

        Args:
            api_key (str, optional): Runware AI API key. If not provided,
                                     will look for key in config manager.
            config_manager (ConfigManager, optional): Config manager instance.
        """
        self.logger = get_logger()
        self.config_manager = config_manager or ConfigManager()

        # Get API key from parameter or config
        self.api_key = api_key
        if not self.api_key:
            # Force reload the config to ensure we have the latest version
            self.config_manager._load_config()

            # Get the API key
            self.api_key = self.config_manager.get_api_key("runware_ai")

            # Debug output
            if self.api_key:
                self.logger.debug(f"Using API key from config: {'*' * 5}{self.api_key[-5:] if self.api_key else 'None'}")
            else:
                self.logger.debug("No API key found in config")
        else:
            self.logger.debug(f"Using provided API key: {'*' * 5}{self.api_key[-5:] if self.api_key else 'None'}")

        if not self.api_key:
            self.logger.error("No API key found in config or provided as parameter")
            raise ValueError("API key must be provided or set in the config file for Runware AI")

        # Initialize available models list
        self.available_models = []
        self._fetch_available_models()

    def _fetch_available_models(self):
        """Fetch available models from the configuration.

        This method retrieves the models from the configuration file instead of
        using hardcoded values, allowing users to define their own models.
        """
        # Get models from the configuration
        config_models = self.config_manager.get_runware_models()

        if not config_models or len(config_models) == 0:
            # If no models are defined in the config, use default models
            self.logger.warning("No Runware AI models found in config, using default models")
            self.available_models = [
                {"id": "air://civitai/4201/130090", "name": "Realistic Vision V6.0"},
                {"id": "air://civitai/101055/128078", "name": "Dreamshaper XL"},
                {"id": "air://civitai/124421/139565", "name": "Juggernaut XL"}
            ]
        else:
            # Use models from the configuration
            self.available_models = config_models

        self.logger.debug(f"Loaded {len(self.available_models)} Runware AI models")

    def get_available_models(self):
        """Get list of available models.

        Returns:
            list: List of available models with id and name.
        """
        return self.available_models

    def generate_image(self, prompt, width=1024, height=768, steps=20, seed=None, model_id=None):
        """Generate an image using the Runware AI API.

        Args:
            prompt (str): Text prompt for image generation
            width (int, optional): Width of the generated image. Defaults to 1024.
            height (int, optional): Height of the generated image. Defaults to 768.
            steps (int, optional): Number of generation steps. Defaults to 20.
            seed (int, optional): Seed for reproducible generation. Defaults to None.
            model_id (str, optional): Model ID to use. Defaults to None.

        Returns:
            PIL.Image.Image: Generated image as PIL Image object

        Raises:
            Exception: If the API request fails
        """
        self.logger.debug(f"Generating image with Runware AI: '{prompt}', size: {width}x{height}, steps: {steps}, seed: {seed}, model: {model_id}")

        # Use a default model if none is specified
        if not model_id:
            # Use the first model from the available models list
            if self.available_models and len(self.available_models) > 0:
                model_id = self.available_models[0]["id"]
                model_name = self.available_models[0]["name"]
                self.logger.debug(f"No model specified, using first available model: {model_name} ({model_id})")
            else:
                # Fallback to a default model if no models are available
                model_id = "runware:100@1"  # Default to FLUX Schnell
                self.logger.debug(f"No models available, using hardcoded default: {model_id}")

        try:
            # Since the Runware SDK is async, we need to use a synchronous wrapper
            # This is a simplified implementation using direct HTTP requests
            # In a production environment, you would use the Runware SDK properly

            # Create a unique task UUID
            task_uuid = str(uuid.uuid4())

            # Prepare the request payload
            payload = [{
                "taskType": "imageInference",
                "taskUUID": task_uuid,
                "outputType": "base64Data",
                "outputFormat": "PNG",
                "positivePrompt": prompt,
                "height": height,
                "width": width,
                "model": model_id,
                "steps": steps,
                "numberResults": 1
            }]

            if seed is not None:
                payload[0]["seed"] = seed

            self.logger.debug(f"Using model: {model_id} for image generation")

            # Make a direct HTTP request to the Runware API
            # Note: In a real implementation, you would use the Runware SDK
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # Make the actual API request to Runware.ai
            api_url = "https://api.runware.ai/v1/tasks"
            self.logger.debug(f"Making API request to Runware.ai: {api_url}")

            try:
                response = requests.post(api_url, json=payload, headers=headers)

                # Check if the request was successful
                if response.status_code == 200:
                    self.logger.debug("API request successful")
                    response_data = response.json()
                else:
                    self.logger.error(f"API request failed with status code {response.status_code}: {response.text}")
                    raise Exception(f"API request failed with status code {response.status_code}: {response.text}")
            except Exception as e:
                self.logger.error(f"Error making API request: {str(e)}")
                raise Exception(f"Error making API request: {str(e)}")

            # Process the response
            if response_data.get("data") and len(response_data["data"]) > 0:
                image_data = response_data["data"][0].get("imageBase64Data")
                if image_data:
                    # Create a PIL Image from the base64 data
                    image_bytes = BytesIO(base64.b64decode(image_data))
                    image = Image.open(image_bytes)

                    # Make a copy to ensure the image is fully loaded
                    image_copy = image.copy()
                    self.logger.debug(f"Successfully generated image: {image_copy.width}x{image_copy.height}")

                    return image_copy
                else:
                    raise Exception("No image data in response")
            else:
                raise Exception("Invalid response from Runware AI API")

        except Exception as e:
            self.logger.error(f"Error generating image with Runware AI: {str(e)}", exc_info=True)
            raise Exception(f"Failed to generate image with Runware AI: {str(e)}")

    def update_api_key(self, api_key):
        """Update the API key.

        Args:
            api_key (str): New API key to use.
        """
        self.logger.debug(f"Updating API key: {'*' * 5}{api_key[-5:] if api_key else 'None'}")
        self.api_key = api_key
