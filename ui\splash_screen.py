"""
Splash screen implementation for the application.
"""
import os
from PyQt6.QtWidgets import QSplashScreen, QLabel, QVBoxLayout, QWidget, QProgressBar, QApplication
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QPainter, QColor, QFont, QLinearGradient, QPen, QBrush
from PyQt6.QtSvg import QSvgRenderer
import time

class SplashScreen(QSplashScreen):
    """Splash screen shown during application startup."""

    def __init__(self, theme="dark"):
        """Initialize the splash screen.

        Args:
            theme (str): The theme to use ("dark" or "light").
        """
        # Create a pixmap for the splash screen with larger dimensions for better quality
        pixmap = QPixmap(1000, 600)  # Increased from 800x500
        pixmap.fill(Qt.GlobalColor.transparent)

        # Initialize the splash screen with the pixmap and keep it on top
        super().__init__(pixmap, Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)
        
        # Set window opacity for a fade-in effect
        self.setWindowOpacity(0)

        # Set up the splash screen content
        self._setup_ui(theme)

        # Set up progress
        self.progress = 0
        self.max_progress = 100
        self.fade_in_complete = False

        # Timer for progress updates and fade effect
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._update_progress)
        self.timer.start(16)  # ~60 FPS for smooth animation

        # Fade in effect
        self.fade_timer = QTimer(self)
        self.fade_timer.timeout.connect(self._fade_in)
        self.fade_timer.start(16)  # ~60 FPS for smooth fade

    def _setup_ui(self, theme):
        """Set up the splash screen UI.

        Args:
            theme (str): The theme to use ("dark" or "light").
        """
        # Create a widget to hold the content
        self.content = QWidget(self)
        self.content.setGeometry(0, 0, 1000, 600)  # Match new dimensions

        # Set up the layout
        layout = QVBoxLayout(self.content)
        layout.setContentsMargins(50, 50, 50, 50)  # Increased padding
        layout.setSpacing(20)  # Increased spacing
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Load the SVG splash screen
        svg_path = os.path.join("ui", "resources", "splash-screen.svg")
        if os.path.exists(svg_path):
            # Create a label for the SVG
            self.svg_label = QLabel()
            self.svg_label.setFixedSize(900, 500)  # Adjusted for new size

            # Load the SVG using QSvgRenderer
            renderer = QSvgRenderer(svg_path)
            pixmap = QPixmap(900, 500)  # Match label size
            pixmap.fill(Qt.GlobalColor.transparent)

            # Paint the SVG onto the pixmap with high quality
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)
            renderer.render(painter)
            painter.end()

            # Set the pixmap to the label
            self.svg_label.setPixmap(pixmap)
            layout.addWidget(self.svg_label)

            # Set the pixmap for the splash screen
            self.setPixmap(pixmap)
        else:
            # Fallback if SVG is not found
            self._setup_fallback_ui(theme)

    def _setup_fallback_ui(self, theme):
        """Set up a fallback UI if the SVG is not found.

        Args:
            theme (str): The theme to use ("dark" or "light").
        """
        # Create a pixmap for the splash screen
        pixmap = QPixmap(1000, 600)  # Match new dimensions

        # Fill with background color based on theme
        if theme == "dark":
            pixmap.fill(QColor("#1a1a1a"))  # Darker background
        else:
            pixmap.fill(QColor("#ffffff"))  # Pure white background

        # Create a painter to draw on the pixmap
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QPainter.RenderHint.TextAntialiasing)

        # Set font for app name with better typography
        font = QFont("Segoe UI", 56, QFont.Weight.Light)  # Lighter weight for modern look
        painter.setFont(font)

        # Create gradient for title text
        if theme == "dark":
            gradient = QLinearGradient(0, 0, pixmap.width(), 0)
            gradient.setColorAt(0, QColor("#2d5af5"))  # Start with blue
            gradient.setColorAt(1, QColor("#45caff"))  # End with light blue
            painter.setPen(QPen(QBrush(gradient), 2))
        else:
            painter.setPen(QColor("#2d5af5"))

        # Draw app name
        title_rect = pixmap.rect()
        title_rect.setBottom(title_rect.center().y() - 30)
        painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, "Azanx Bulk AI Images")

        # Set font for description
        font = QFont("Segoe UI", 24, QFont.Weight.Light)
        painter.setFont(font)

        # Set description color with slight transparency
        if theme == "dark":
            painter.setPen(QColor(255, 255, 255, 200))
        else:
            painter.setPen(QColor(70, 70, 70, 200))

        # Draw description
        desc_rect = pixmap.rect()
        desc_rect.setTop(title_rect.bottom() + 20)
        desc_rect.setBottom(desc_rect.center().y() + 30)
        painter.drawText(desc_rect, Qt.AlignmentFlag.AlignCenter, "Generate beautiful AI images in bulk")

        # Set font for footer with elegant styling
        font = QFont("Segoe UI", 14, QFont.Weight.Light)
        font.setItalic(True)
        painter.setFont(font)

        # Set footer color with more transparency
        if theme == "dark":
            painter.setPen(QColor(255, 255, 255, 150))
        else:
            painter.setPen(QColor(70, 70, 70, 150))

        # Draw footer
        footer_rect = pixmap.rect()
        footer_rect.setTop(footer_rect.bottom() - 80)
        painter.drawText(footer_rect, Qt.AlignmentFlag.AlignCenter, "Made with love in Pakistan")

        # End painting
        painter.end()

        # Set the pixmap for the splash screen
        self.setPixmap(pixmap)

        # Add a modern progress bar
        self.progress_bar = QProgressBar(self)
        self.progress_bar.setGeometry(200, 400, 600, 8)  # Thinner, wider progress bar
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)

        # Style the progress bar with modern design
        progress_style = """
            QProgressBar {
                background-color: %s;
                border-radius: 4px;
                border: none;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2d5af5, stop:1 #45caff);
                border-radius: 4px;
            }
        """ % ("#333333" if theme == "dark" else "#e8e8e8")
        
        self.progress_bar.setStyleSheet(progress_style)

    def _update_progress(self):
        """Update the progress animation with smooth transitions."""
        if not self.fade_in_complete:
            return
            
        self.progress += 1
        if self.progress > self.max_progress:
            self.timer.stop()
            return

        # If we have a fallback progress bar, update it with smooth animation
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setValue(self.progress)

        # Update the splash screen
        self.repaint()

    def _fade_in(self):
        """Create a smooth fade-in effect when the splash screen appears."""
        if not self.fade_in_complete:
            opacity = self.windowOpacity() + 0.05
            if opacity >= 1.0:
                opacity = 1.0
                self.fade_in_complete = True
                self.fade_timer.stop()
            self.setWindowOpacity(opacity)

    def set_message(self, message):
        """Set a message to display on the splash screen with fade effect.

        Args:
            message (str): The message to display.
        """
        # Draw the message on the splash screen with shadow effect for better readability
        if hasattr(self, 'progress_bar'):
            y_pos = self.progress_bar.y() - 30
        else:
            y_pos = self.height() - 100

        # Create semi-transparent background for text
        msg_rect = self.rect()
        msg_rect.setTop(y_pos)
        msg_rect.setHeight(25)
        
        # Show message with shadow effect
        self.showMessage(
            message,
            Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignHCenter,
            QColor(255, 255, 255, 220)  # Slightly transparent white
        )

    def set_message_position(self, message, position='bottom'):
        """Set a message at a specific position on the splash screen.

        Args:
            message (str): The message to display.
            position (str): Position of the message ('top', 'center', or 'bottom').
        """
        alignment = Qt.AlignmentFlag.AlignHCenter
        if position == 'top':
            alignment |= Qt.AlignmentFlag.AlignTop
        elif position == 'center':
            alignment |= Qt.AlignmentFlag.AlignVCenter
        else:  # bottom
            alignment |= Qt.AlignmentFlag.AlignBottom

        self.showMessage(message, alignment, QColor(255, 255, 255, 220))

    def _create_scaled_pixmap(self, original_pixmap):
        """Create a properly scaled pixmap maintaining aspect ratio and adding shadow effects.

        Args:
            original_pixmap (QPixmap): The original pixmap to scale.

        Returns:
            QPixmap: The scaled and enhanced pixmap.
        """
        # Create a new pixmap with the splash screen dimensions
        scaled_pixmap = QPixmap(self.width(), self.height())
        scaled_pixmap.fill(Qt.GlobalColor.transparent)

        # Create a painter for the new pixmap
        painter = QPainter(scaled_pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)

        # Calculate scaling while maintaining aspect ratio
        target_rect = scaled_pixmap.rect()
        source_rect = original_pixmap.rect()
        
        # Scale the source rect to fit the target while maintaining aspect ratio
        scaled_rect = source_rect
        scaled_rect.setSize(source_rect.size().scaled(
            target_rect.size(), 
            Qt.AspectRatioMode.KeepAspectRatio
        ))
        
        # Center the scaled rect in the target
        scaled_rect.moveCenter(target_rect.center())
        
        # Draw the scaled image
        painter.drawPixmap(scaled_rect, original_pixmap, source_rect)
        painter.end()

        return scaled_pixmap

    def set_progress(self, value):
        """Set the progress value with validation.

        Args:
            value (int): The progress value (0-100).
        """
        # Validate progress value
        value = max(0, min(100, value))
        self.progress = value

        # If we have a fallback progress bar, update it with animation
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setValue(value)
            
            # Update the progress bar color based on progress
            if value == 100:
                self.progress_bar.setStyleSheet(self.progress_bar.styleSheet().replace(
                    'stop:0 #2d5af5',
                    'stop:0 #00c853'  # Change to green when complete
                ))

        # Update the splash screen
        self.repaint()

    @staticmethod
    def simulate_loading(splash, app, min_duration=2.0, max_duration=None):
        """Simulate a loading process with smooth transitions.

        Args:
            splash (SplashScreen): The splash screen instance.
            app (QApplication): The application instance.
            min_duration (float): Minimum duration in seconds to show the splash screen.
            max_duration (float, optional): Maximum duration in seconds.
        """
        import time
        from math import sin, pi

        start_time = time.time()
        steps = 100

        # Calculate step time with smoother progression
        step_time = min_duration / steps

        # Simulate loading process with splash screen updates
        for i in range(1, steps + 1):
            # Use sine function for smooth progress animation
            progress = int(50 * (1 + sin(i * pi / steps)))
            
            # Update messages at specific points with meaningful updates
            if i == int(steps * 0.1):
                splash.set_message("Initializing application components...")
            elif i == int(steps * 0.3):
                splash.set_message("Loading configuration settings...")
            elif i == int(steps * 0.5):
                splash.set_message("Preparing user interface...")
            elif i == int(steps * 0.7):
                splash.set_message("Establishing service connections...")
            elif i == int(steps * 0.85):
                splash.set_message("Finalizing application setup...")
            elif i == int(steps * 0.95):
                splash.set_message("Ready to launch...")

            # Update progress with smooth animation
            splash.set_progress(progress)
            app.processEvents()

            # Calculate timing for smooth progression
            elapsed = time.time() - start_time

            if max_duration and elapsed >= max_duration:
                splash.set_progress(100)
                app.processEvents()
                break

            # Adjust sleep time for smoother animation
            if elapsed < min_duration:
                remaining = min(step_time - (elapsed / steps), 0.016)  # Cap at ~60 FPS
                if remaining > 0:
                    time.sleep(remaining)
            else:
                # Minimal sleep for visual smoothness
                time.sleep(0.016)

    def finish(self, window=None):
        """Hide the splash screen with a fade-out effect.

        Args:
            window (QWidget, optional): Main window to activate. Defaults to None.
        """
        # Ensure we show the 100% completion state
        self.set_progress(100)
        self.set_message("Loading complete!")
        QApplication.processEvents()
        
        # Small pause to show completion
        time.sleep(0.2)
        
        # Create a smooth fade-out effect
        current_opacity = 1.0
        steps = 30
        fade_duration = 0.5  # seconds
        step_time = fade_duration / steps
        
        for i in range(steps):
            current_opacity -= 1.0 / steps
            self.setWindowOpacity(max(0, current_opacity))
            QApplication.processEvents()
            time.sleep(step_time)
            
        # Call parent's finish method
        super().finish(window)
