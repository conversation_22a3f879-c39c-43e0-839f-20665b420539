#!/usr/bin/env python3
"""
Complete build and test script for BulkAI Desktop Application.

This script combines the build process and testing into a single workflow.
"""

import sys
import subprocess
from pathlib import Path


def run_command(command, description):
    """Run a command and return success status."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Exit code: {e.returncode}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False


def main():
    """Main build and test workflow."""
    print("🚀 BulkAI Desktop Application - Complete Build & Test Workflow")
    print("=" * 70)
    
    # Step 1: Build the executable
    if not run_command("python build_executable.py --clean", "Building executable"):
        print("\n❌ Build failed. Stopping workflow.")
        return 1
    
    print()
    
    # Step 2: Test the executable
    if not run_command("python test_executable.py", "Testing executable"):
        print("\n❌ Testing failed. The executable may have issues.")
        return 1
    
    print()
    print("=" * 70)
    print("🎉 Build and test workflow completed successfully!")
    print()
    print("📦 Your standalone executable is ready:")
    
    # Show final information
    executable_path = Path("dist") / "Azanx Bulk AI Images.exe"
    if executable_path.exists():
        file_size = executable_path.stat().st_size / (1024 * 1024)
        print(f"   📍 Location: {executable_path.absolute()}")
        print(f"   📊 Size: {file_size:.1f} MB")
        print()
        print("💡 Distribution notes:")
        print("   • The executable is completely standalone")
        print("   • No Python installation required on target machines")
        print("   • All dependencies are bundled")
        print("   • Ready for distribution to end users")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
